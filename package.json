{"name": "marketplaymaker", "version": "0.1.0", "private": true, "dependencies": {"@babel/preset-env": "^7.26.7", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@date-io/date-fns": "^3.0.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@fullcalendar/common": "^5.11.5", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@mui/icons-material": "^6.1.5", "@mui/material": "^6.1.5", "@mui/x-date-pickers": "^7.24.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@stripe/react-stripe-js": "^2.7.2", "@stripe/stripe-js": "^4.3.0", "@tailwindcss/postcss7-compat": "^2.2.17", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.2", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "draft-js": "^0.11.7", "firebase": "^10.12.5", "firebase-admin": "^12.3.0", "framer-motion": "^11.3.21", "http-proxy-middleware": "^3.0.3", "lucide-react": "^0.453.0", "metaapi.cloud-sdk": "^23.1.1", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primereact": "^10.9.2", "react": "^18.3.1", "react-calendar": "^5.0.0", "react-chartjs-2": "^5.2.0", "react-day-picker": "^9.5.1", "react-dom": "^18.3.1", "react-firebase-hooks": "^5.1.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.3.0", "react-lazy-load-image-component": "^1.6.2", "react-refresh": "^0.16.0", "react-router-dom": "^6.26.0", "react-scripts": "^3.0.1", "react-telegram-login": "^1.1.2", "recharts": "^2.12.7", "stripe": "^16.8.0", "styled-components": "^6.1.11", "tailwind-merge": "^2.6.0", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "tailwindcss-animate": "^1.0.7", "web-vitals": "^2.1.4"}, "scripts": {"prepare": "husky install", "start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "lint:fix": "eslint 'src/**/*.{js,jsx,ts,tsx}' --fix", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,json,css,scss,md}'", "format:check": "prettier --check 'src/**/*.{js,jsx,ts,tsx,json,css,scss,md}'", "fix-all": "npm run format && npm run lint:fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@craco/craco": "^5.9.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@rollup/plugin-terser": "^0.4.4", "assert": "^2.1.0", "autoprefixer": "^10.4.17", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "https-browserify": "^1.0.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "os-browserify": "^0.3.0", "postcss": "^8.4.35", "postcss-flexbugs-fixes": "^5.0.2", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^9.3.0", "prettier": "^3.4.2", "process": "^0.11.10", "source-map-loader": "^5.0.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "webpack": "^5.97.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0"}}