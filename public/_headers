# Security Headers for Production
/*
  # Prevent clickjacking
  X-Frame-Options: DENY
  
  # Prevent MIME type sniffing
  X-Content-Type-Options: nosniff
  
  # Enable XSS protection
  X-XSS-Protection: 1; mode=block
  
  # Strict Transport Security
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://s3.tradingview.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.stripe.com https://metastats-api-v1.london.agiliumtrade.ai https://mt-client-api-v1.london.agiliumtrade.ai; frame-src https://js.stripe.com;
  
  # Referrer Policy
  Referrer-Policy: strict-origin-when-cross-origin
  
  # Permissions Policy
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()
  
  # Cache Control
  Cache-Control: public, max-age=31536000, immutable
