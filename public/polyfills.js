// Polyfills for Node.js modules in browser environment
(function() {
  'use strict';

  // Process polyfill - make it available globally and as a module
  var processPolyfill = {
    env: {
      NODE_ENV: 'development'
    },
    browser: true,
    version: '',
    versions: {},
    nextTick: function(fn) {
      setTimeout(fn, 0);
    },
    cwd: function() {
      return '/';
    },
    chdir: function() {
      // no-op
    }
  };

  if (typeof window !== 'undefined') {
    window.process = processPolyfill;
    // Also make it available as a global for webpack
    if (typeof global === 'undefined') {
      window.global = window;
    }
    global.process = processPolyfill;
  }

  // URL polyfill with format function
  var urlPolyfill = {
    format: function(urlObject) {
      if (typeof urlObject === 'string') {
        return urlObject;
      }

      var protocol = urlObject.protocol || '';
      var hostname = urlObject.hostname || urlObject.host || '';
      var port = urlObject.port ? ':' + urlObject.port : '';
      var pathname = urlObject.pathname || '';
      var search = urlObject.search || '';
      var hash = urlObject.hash || '';

      return protocol + '//' + hostname + port + pathname + search + hash;
    },

    parse: function(urlString) {
      try {
        var url = new URL(urlString);
        return {
          protocol: url.protocol,
          hostname: url.hostname,
          port: url.port,
          pathname: url.pathname,
          search: url.search,
          hash: url.hash,
          host: url.host
        };
      } catch (e) {
        return {};
      }
    }
  };

  if (typeof window !== 'undefined') {
    window.url = urlPolyfill;
    // Also make it available globally
    if (window.global) {
      window.global.url = urlPolyfill;
    }
  }

  // Buffer polyfill
  var bufferPolyfill = {
    isBuffer: function() { return false; },
    from: function(data) { return data; },
    alloc: function(size) { return new Array(size).fill(0); }
  };

  if (typeof window !== 'undefined') {
    window.Buffer = bufferPolyfill;
    if (window.global) {
      window.global.Buffer = bufferPolyfill;
    }
  }

  // Basic crypto polyfill
  if (typeof window !== 'undefined' && !window.crypto) {
    window.crypto = {
      getRandomValues: function(array) {
        for (var i = 0; i < array.length; i++) {
          array[i] = Math.floor(Math.random() * 256);
        }
        return array;
      }
    };
  }

  // Override require for specific modules if needed
  if (typeof window !== 'undefined' && typeof window.require === 'undefined') {
    window.require = function(moduleName) {
      switch (moduleName) {
        case 'url':
          return urlPolyfill;
        case 'process':
          return processPolyfill;
        case 'buffer':
          return { Buffer: bufferPolyfill };
        default:
          throw new Error('Module not found: ' + moduleName);
      }
    };
  }

  console.log('Polyfills loaded successfully');
})();
