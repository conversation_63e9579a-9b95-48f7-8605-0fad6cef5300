// Polyfills for Node.js modules in browser environment
(function() {
  'use strict';

  // Process polyfill
  if (typeof window !== 'undefined' && !window.process) {
    window.process = {
      env: {
        NODE_ENV: 'development'
      },
      browser: true,
      version: '',
      versions: {},
      nextTick: function(fn) {
        setTimeout(fn, 0);
      },
      cwd: function() {
        return '/';
      },
      chdir: function() {
        // no-op
      }
    };
  }

  // Global polyfill
  if (typeof window !== 'undefined' && !window.global) {
    window.global = window;
  }

  // Buffer polyfill (basic)
  if (typeof window !== 'undefined' && !window.Buffer) {
    window.Buffer = {
      isBuffer: function() { return false; },
      from: function(data) { return data; },
      alloc: function(size) { return new Array(size).fill(0); }
    };
  }

  // URL polyfill
  if (typeof window !== 'undefined' && typeof window.URL !== 'undefined' && !window.URL.format) {
    // Add basic URL.format function
    if (!window.url) {
      window.url = {};
    }
    window.url.format = function(urlObject) {
      if (typeof urlObject === 'string') {
        return urlObject;
      }
      
      var protocol = urlObject.protocol || '';
      var hostname = urlObject.hostname || urlObject.host || '';
      var port = urlObject.port ? ':' + urlObject.port : '';
      var pathname = urlObject.pathname || '';
      var search = urlObject.search || '';
      var hash = urlObject.hash || '';
      
      return protocol + '//' + hostname + port + pathname + search + hash;
    };
    
    window.url.parse = function(urlString) {
      try {
        var url = new URL(urlString);
        return {
          protocol: url.protocol,
          hostname: url.hostname,
          port: url.port,
          pathname: url.pathname,
          search: url.search,
          hash: url.hash,
          host: url.host
        };
      } catch (e) {
        return {};
      }
    };
  }

  // Basic crypto polyfill
  if (typeof window !== 'undefined' && !window.crypto) {
    window.crypto = {
      getRandomValues: function(array) {
        for (var i = 0; i < array.length; i++) {
          array[i] = Math.floor(Math.random() * 256);
        }
        return array;
      }
    };
  }

  console.log('Polyfills loaded successfully');
})();
