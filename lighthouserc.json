{"ci": {"collect": {"url": ["http://localhost:3000"], "startServerCommand": "npm start", "startServerReadyPattern": "Local:", "numberOfRuns": 3}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.9}], "categories:accessibility": ["error", {"minScore": 0.95}], "categories:best-practices": ["warn", {"minScore": 0.9}], "categories:seo": ["warn", {"minScore": 0.95}], "categories:pwa": ["warn", {"minScore": 0.8}]}}, "upload": {"target": "temporary-public-storage"}}}