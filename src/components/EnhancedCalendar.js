import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';
import { TrendingUp, TrendingDown, Calendar as CalendarIcon, Target, DollarSign } from 'lucide-react';

const CalendarContainer = styled.div`
  background: #1a1a1a;
  border-radius: 15px;
  padding: 2rem;
  color: white;
  border: 1px solid #333;
  position: relative;
`;

const CalendarHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const MonthStats = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  font-size: 0.9rem;
`;

const StatBadge = styled.div`
  background: ${props => props.positive ? '#22c55e' : props.negative ? '#ef4444' : '#6b7280'};
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.8rem;
`;

const StyledCalendar = styled(Calendar)`
  width: 100%;
  max-width: none;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  color: white;
  font-family: inherit;
  
  .react-calendar__navigation {
    background: #333;
    border-bottom: 1px solid #444;
    border-radius: 12px 12px 0 0;
    
    button {
      background: transparent;
      color: white;
      border: none;
      font-size: 1rem;
      font-weight: bold;
      padding: 1rem;
      
      &:hover {
        background: #444;
      }
      
      &:disabled {
        opacity: 0.5;
      }
    }
  }
  
  .react-calendar__month-view__weekdays {
    background: #333;
    border-bottom: 1px solid #444;
    
    .react-calendar__month-view__weekdays__weekday {
      padding: 0.75rem 0.5rem;
      font-weight: bold;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 1px;
      
      abbr {
        text-decoration: none;
      }
    }
  }
  
  .react-calendar__tile {
    background: #2a2a2a;
    border: 1px solid #444;
    color: white;
    padding: 0.75rem 0.5rem;
    position: relative;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    transition: all 0.3s ease;
    
    &:hover {
      background: #333;
      border-color: #555;
      transform: scale(1.02);
    }
    
    &.react-calendar__tile--active {
      background: white !important;
      color: black !important;
      border-color: white;
      
      .tile-content {
        color: black;
      }
    }
    
    &.react-calendar__tile--now {
      border-color: #60a5fa;
      box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3);
    }
  }
  
  .react-calendar__month-view__days__day--neighboringMonth {
    opacity: 0.3;
  }
`;

const TileContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-top: 0.25rem;
  
  .date-number {
    font-weight: bold;
    margin-bottom: 0.25rem;
  }
  
  .pnl-indicator {
    font-size: 0.7rem;
    font-weight: bold;
    padding: 0.1rem 0.4rem;
    border-radius: 10px;
    margin-bottom: 0.1rem;
    min-width: 40px;
    text-align: center;
  }
  
  .trade-count {
    font-size: 0.6rem;
    opacity: 0.8;
    display: flex;
    align-items: center;
    gap: 0.2rem;
  }
`;

const SelectedDatePanel = styled.div`
  background: #333;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
  border: 1px solid #444;
`;

const DateHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
  
  h3 {
    margin: 0;
    color: white;
  }
`;

const QuickStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const QuickStat = styled.div`
  background: #2a2a2a;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #444;
  
  .label {
    font-size: 0.7rem;
    opacity: 0.7;
    text-transform: uppercase;
    margin-bottom: 0.25rem;
  }
  
  .value {
    font-size: 1.2rem;
    font-weight: bold;
    color: ${props => props.positive ? '#22c55e' : props.negative ? '#ef4444' : 'white'};
  }
`;

const TradesList = styled.div`
  max-height: 300px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #333;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 3px;
    
    &:hover {
      background: #666;
    }
  }
`;

const TradeItem = styled.div`
  background: #2a2a2a;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.5rem;
  border: 1px solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  
  &:hover {
    background: #333;
    border-color: #555;
  }
`;

const TradeInfo = styled.div`
  .pair {
    font-weight: bold;
    margin-bottom: 0.25rem;
  }
  
  .details {
    font-size: 0.8rem;
    opacity: 0.7;
  }
`;

const TradePnL = styled.div`
  font-weight: bold;
  font-size: 1.1rem;
  color: ${props => props.positive ? '#22c55e' : '#ef4444'};
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 2rem;
  opacity: 0.7;
  
  .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }
`;

const formatDate = (date) => {
  const d = new Date(date);
  const month = `${d.getMonth() + 1}`.padStart(2, '0');
  const day = `${d.getDate()}`.padStart(2, '0');
  const year = d.getFullYear();
  return `${year}-${month}-${day}`;
};

const EnhancedCalendar = ({ trades = [], onTradeSelect, onDateSelect }) => {
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Process trades data for calendar display
  const { dailyData, monthStats } = useMemo(() => {
    const daily = {};
    let monthTotal = 0;
    let monthTrades = 0;
    let monthWins = 0;

    trades.forEach(trade => {
      const date = formatDate(trade.date);
      const pnl = parseFloat(trade.pnl || 0);
      
      if (!daily[date]) {
        daily[date] = { trades: [], totalPnL: 0, wins: 0, losses: 0 };
      }
      
      daily[date].trades.push(trade);
      daily[date].totalPnL += pnl;
      
      if (pnl > 0) daily[date].wins++;
      else if (pnl < 0) daily[date].losses++;
      
      // Month stats (current month)
      const tradeDate = new Date(trade.date);
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      
      if (tradeDate.getMonth() === currentMonth && tradeDate.getFullYear() === currentYear) {
        monthTotal += pnl;
        monthTrades++;
        if (pnl > 0) monthWins++;
      }
    });

    return {
      dailyData: daily,
      monthStats: {
        total: monthTotal,
        trades: monthTrades,
        winRate: monthTrades > 0 ? (monthWins / monthTrades) * 100 : 0
      }
    };
  }, [trades]);

  const selectedDateData = dailyData[formatDate(selectedDate)] || { trades: [], totalPnL: 0, wins: 0, losses: 0 };

  const tileContent = ({ date, view }) => {
    if (view !== 'month') return null;
    
    const dateStr = formatDate(date);
    const dayData = dailyData[dateStr];
    
    if (!dayData || dayData.trades.length === 0) return null;
    
    const { totalPnL, trades: dayTrades } = dayData;
    const isPositive = totalPnL > 0;
    
    return (
      <TileContent className="tile-content">
        <div 
          className="pnl-indicator"
          style={{
            background: isPositive ? '#22c55e' : '#ef4444',
            color: 'white'
          }}
        >
          ${Math.abs(totalPnL).toFixed(0)}
        </div>
        <div className="trade-count">
          <Target size={8} />
          {dayTrades.length}
        </div>
      </TileContent>
    );
  };

  const handleDateChange = (date) => {
    setSelectedDate(date);
    if (onDateSelect) onDateSelect(date);
  };

  return (
    <CalendarContainer>
      <CalendarHeader>
        <h2>
          <CalendarIcon size={24} />
          Trading Calendar
        </h2>
        <MonthStats>
          <StatBadge positive={monthStats.total > 0} negative={monthStats.total < 0}>
            <DollarSign size={12} style={{ marginRight: '0.25rem' }} />
            ${monthStats.total.toFixed(0)}
          </StatBadge>
          <StatBadge>
            {monthStats.trades} trades
          </StatBadge>
          <StatBadge positive={monthStats.winRate > 50}>
            {monthStats.winRate.toFixed(0)}% win rate
          </StatBadge>
        </MonthStats>
      </CalendarHeader>

      <StyledCalendar
        onChange={handleDateChange}
        value={selectedDate}
        tileContent={tileContent}
        showNeighboringMonth={false}
      />

      <SelectedDatePanel>
        <DateHeader>
          <h3>{selectedDate.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}</h3>
        </DateHeader>

        {selectedDateData.trades.length > 0 ? (
          <>
            <QuickStats>
              <QuickStat positive={selectedDateData.totalPnL > 0} negative={selectedDateData.totalPnL < 0}>
                <div className="label">Total P&L</div>
                <div className="value">${selectedDateData.totalPnL.toFixed(2)}</div>
              </QuickStat>
              <QuickStat>
                <div className="label">Trades</div>
                <div className="value">{selectedDateData.trades.length}</div>
              </QuickStat>
              <QuickStat positive={selectedDateData.wins > selectedDateData.losses}>
                <div className="label">Win Rate</div>
                <div className="value">
                  {selectedDateData.trades.length > 0 
                    ? ((selectedDateData.wins / selectedDateData.trades.length) * 100).toFixed(0)
                    : 0}%
                </div>
              </QuickStat>
              <QuickStat>
                <div className="label">Best Trade</div>
                <div className="value">
                  ${Math.max(...selectedDateData.trades.map(t => parseFloat(t.pnl || 0))).toFixed(2)}
                </div>
              </QuickStat>
            </QuickStats>

            <TradesList>
              {selectedDateData.trades.map((trade, index) => {
                const pnl = parseFloat(trade.pnl || 0);
                return (
                  <TradeItem 
                    key={trade.id || index}
                    onClick={() => onTradeSelect && onTradeSelect(trade)}
                    style={{ cursor: onTradeSelect ? 'pointer' : 'default' }}
                  >
                    <TradeInfo>
                      <div className="pair">{trade.pair}</div>
                      <div className="details">
                        {trade.direction} • {trade.outcome || 'N/A'}
                      </div>
                    </TradeInfo>
                    <TradePnL positive={pnl > 0}>
                      {pnl > 0 ? <TrendingUp size={16} /> : <TrendingDown size={16} />}
                      ${Math.abs(pnl).toFixed(2)}
                    </TradePnL>
                  </TradeItem>
                );
              })}
            </TradesList>
          </>
        ) : (
          <EmptyState>
            <div className="icon">📅</div>
            <h4>No trades on this date</h4>
            <p>Select a date with trades to see details</p>
          </EmptyState>
        )}
      </SelectedDatePanel>
    </CalendarContainer>
  );
};

export default EnhancedCalendar;
