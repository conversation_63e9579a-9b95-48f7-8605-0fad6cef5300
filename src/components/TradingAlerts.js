import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Bell, TrendingUp, TrendingDown, Al<PERSON><PERSON>riangle, CheckCircle, Clock, Target } from 'lucide-react';

const AlertsContainer = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  padding: 2rem;
  color: white;
  margin-bottom: 2rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const AlertsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const AlertCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border-left: 4px solid ${props => {
    switch(props.type) {
      case 'buy': return '#4ade80';
      case 'sell': return '#f87171';
      case 'warning': return '#fbbf24';
      default: return '#60a5fa';
    }
  }};
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
`;

const AlertHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const AlertType = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 0.9rem;
  
  &.buy { color: #4ade80; }
  &.sell { color: #f87171; }
  &.warning { color: #fbbf24; }
  &.info { color: #60a5fa; }
`;

const AlertTime = styled.div`
  font-size: 0.8rem;
  opacity: 0.7;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const AlertContent = styled.div`
  margin-bottom: 1rem;
  
  h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
  }
  
  p {
    margin: 0;
    opacity: 0.9;
    line-height: 1.4;
  }
`;

const AlertMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
`;

const Metric = styled.div`
  text-align: center;
  
  .label {
    font-size: 0.7rem;
    opacity: 0.7;
    text-transform: uppercase;
    margin-bottom: 0.25rem;
  }
  
  .value {
    font-weight: bold;
    font-size: 0.9rem;
  }
`;

const CreateAlertForm = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
`;

const InputGroup = styled.div`
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: bold;
  }
  
  input, select {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
  }
`;

const AddButton = styled.button`
  background: linear-gradient(45deg, #4ade80, #22c55e);
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
`;

const TradingAlerts = () => {
  const [alerts, setAlerts] = useState([]);
  const [newAlert, setNewAlert] = useState({
    pair: 'EURUSD',
    type: 'price',
    condition: 'above',
    value: '',
    message: ''
  });

  // Mock alerts data
  useEffect(() => {
    const mockAlerts = [
      {
        id: 1,
        type: 'buy',
        pair: 'EURUSD',
        title: 'Strong Buy Signal',
        message: 'RSI oversold + bullish divergence detected. Entry at 1.0850 with SL at 1.0800.',
        time: new Date(Date.now() - 5 * 60000),
        metrics: {
          entry: '1.0850',
          sl: '1.0800',
          tp: '1.0950',
          rrr: '2:1'
        }
      },
      {
        id: 2,
        type: 'sell',
        pair: 'GBPUSD',
        title: 'Resistance Break Failed',
        message: 'Failed to break 1.2700 resistance. Consider short position with tight SL.',
        time: new Date(Date.now() - 15 * 60000),
        metrics: {
          entry: '1.2680',
          sl: '1.2720',
          tp: '1.2600',
          rrr: '2:1'
        }
      },
      {
        id: 3,
        type: 'warning',
        pair: 'USDJPY',
        title: 'High Impact News Alert',
        message: 'NFP release in 30 minutes. Expect high volatility. Consider closing positions.',
        time: new Date(Date.now() - 2 * 60000),
        metrics: {
          impact: 'High',
          time: '30min',
          pairs: 'USD/*',
          action: 'Caution'
        }
      },
      {
        id: 4,
        type: 'info',
        pair: 'AUDUSD',
        title: 'Support Level Hold',
        message: 'Price holding above 0.6700 support. Watch for bounce confirmation.',
        time: new Date(Date.now() - 45 * 60000),
        metrics: {
          support: '0.6700',
          resistance: '0.6780',
          trend: 'Bullish',
          strength: 'Medium'
        }
      }
    ];
    setAlerts(mockAlerts);
  }, []);

  const getAlertIcon = (type) => {
    switch(type) {
      case 'buy': return <TrendingUp size={16} />;
      case 'sell': return <TrendingDown size={16} />;
      case 'warning': return <AlertTriangle size={16} />;
      default: return <CheckCircle size={16} />;
    }
  };

  const formatTime = (time) => {
    const now = new Date();
    const diff = Math.floor((now - time) / 60000); // minutes
    
    if (diff < 1) return 'Just now';
    if (diff < 60) return `${diff}m ago`;
    if (diff < 1440) return `${Math.floor(diff / 60)}h ago`;
    return time.toLocaleDateString();
  };

  const handleAddAlert = () => {
    if (!newAlert.value || !newAlert.message) return;
    
    const alert = {
      id: Date.now(),
      type: 'info',
      pair: newAlert.pair,
      title: `Custom Alert - ${newAlert.pair}`,
      message: newAlert.message,
      time: new Date(),
      metrics: {
        condition: `${newAlert.condition} ${newAlert.value}`,
        type: newAlert.type
      }
    };
    
    setAlerts([alert, ...alerts]);
    setNewAlert({ pair: 'EURUSD', type: 'price', condition: 'above', value: '', message: '' });
  };

  return (
    <AlertsContainer>
      <Header>
        <h2>
          <Bell size={24} />
          Trading Alerts & Signals
        </h2>
        <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>
          {alerts.length} active alerts
        </div>
      </Header>

      <CreateAlertForm>
        <h3>Create Custom Alert</h3>
        <FormGrid>
          <InputGroup>
            <label>Currency Pair</label>
            <select 
              value={newAlert.pair} 
              onChange={(e) => setNewAlert({...newAlert, pair: e.target.value})}
            >
              <option value="EURUSD">EUR/USD</option>
              <option value="GBPUSD">GBP/USD</option>
              <option value="USDJPY">USD/JPY</option>
              <option value="AUDUSD">AUD/USD</option>
              <option value="USDCAD">USD/CAD</option>
            </select>
          </InputGroup>
          <InputGroup>
            <label>Alert Type</label>
            <select 
              value={newAlert.type} 
              onChange={(e) => setNewAlert({...newAlert, type: e.target.value})}
            >
              <option value="price">Price Alert</option>
              <option value="indicator">Indicator Signal</option>
              <option value="news">News Event</option>
            </select>
          </InputGroup>
          <InputGroup>
            <label>Condition</label>
            <select 
              value={newAlert.condition} 
              onChange={(e) => setNewAlert({...newAlert, condition: e.target.value})}
            >
              <option value="above">Above</option>
              <option value="below">Below</option>
              <option value="crosses">Crosses</option>
            </select>
          </InputGroup>
          <InputGroup>
            <label>Value</label>
            <input 
              type="number" 
              step="0.00001"
              value={newAlert.value}
              onChange={(e) => setNewAlert({...newAlert, value: e.target.value})}
              placeholder="1.0850"
            />
          </InputGroup>
        </FormGrid>
        <InputGroup style={{ marginBottom: '1rem' }}>
          <label>Alert Message</label>
          <input 
            type="text"
            value={newAlert.message}
            onChange={(e) => setNewAlert({...newAlert, message: e.target.value})}
            placeholder="Enter your alert message..."
          />
        </InputGroup>
        <AddButton onClick={handleAddAlert}>
          <Target size={16} style={{ marginRight: '0.5rem' }} />
          Create Alert
        </AddButton>
      </CreateAlertForm>

      <AlertsGrid>
        {alerts.map(alert => (
          <AlertCard key={alert.id} type={alert.type}>
            <AlertHeader>
              <AlertType className={alert.type}>
                {getAlertIcon(alert.type)}
                {alert.type} Signal
              </AlertType>
              <AlertTime>
                <Clock size={12} />
                {formatTime(alert.time)}
              </AlertTime>
            </AlertHeader>
            
            <AlertContent>
              <h4>{alert.pair} - {alert.title}</h4>
              <p>{alert.message}</p>
            </AlertContent>
            
            <AlertMetrics>
              {Object.entries(alert.metrics).map(([key, value]) => (
                <Metric key={key}>
                  <div className="label">{key}</div>
                  <div className="value">{value}</div>
                </Metric>
              ))}
            </AlertMetrics>
          </AlertCard>
        ))}
      </AlertsGrid>
    </AlertsContainer>
  );
};

export default TradingAlerts;
