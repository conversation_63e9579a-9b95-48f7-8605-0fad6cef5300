import React from 'react';
import { Helmet } from 'react-helmet-async';

const SEO = ({
  title = 'MarketPlayMaker - Premium Trading Insights',
  description = 'Exclusive premium market insights and trading tools for professional traders.',
  keywords = 'trading, forex, stocks, market analysis, trading journal, MetaTrader',
  image = '/logo2.png',
  url = window.location.href,
  type = 'website',
  author = 'MarketPlayMaker',
  publishedTime,
  modifiedTime
}) => {
  const siteTitle = 'MarketPlayMaker';
  const fullTitle = title.includes(siteTitle) ? title : `${title} | ${siteTitle}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      <link rel="canonical" href={url} />

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteTitle} />
      <meta property="og:locale" content="en_US" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:creator" content="@marketplaymaker" />

      {/* Article Meta Tags (for blog posts) */}
      {type === 'article' && (
        <>
          <meta property="article:author" content={author} />
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          <meta property="article:section" content="Trading" />
          <meta property="article:tag" content={keywords} />
        </>
      )}

      {/* Additional SEO Meta Tags */}
      <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />
      
      {/* Mobile Optimization */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Theme Color */}
      <meta name="theme-color" content="#000000" />
      <meta name="msapplication-TileColor" content="#000000" />
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://js.stripe.com" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="https://s3.tradingview.com" />
      <link rel="dns-prefetch" href="https://metastats-api-v1.london.agiliumtrade.ai" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": siteTitle,
          "description": description,
          "url": url,
          "applicationCategory": "FinanceApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "category": "Trading Tools"
          },
          "author": {
            "@type": "Organization",
            "name": author
          }
        })}
      </script>
    </Helmet>
  );
};

export default SEO;
