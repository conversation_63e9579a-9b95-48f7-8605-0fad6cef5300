import { useEffect, useRef } from 'react';
import { reportWebVitals, monitorMemoryUsage, monitorNetworkStatus } from '../utils/performance';
import { useApp } from '../context/AppContext';

const PerformanceMonitor = ({ enableNetworkNotifications = false }) => {
  const { actions } = useApp();
  const initialized = useRef(false);

  // Web Vitals monitoring - only run once
  useEffect(() => {
    if (!initialized.current) {
      try {
        reportWebVitals((metric) => {
          console.log('Web Vital:', metric);

          // Send to analytics service
          if (window.gtag) {
            window.gtag('event', metric.name, {
              event_category: 'Web Vitals',
              value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
              event_label: metric.id,
              non_interaction: true,
            });
          }
        });
        initialized.current = true;
      } catch (error) {
        console.error('Web Vitals monitoring error:', error);
      }
    }
  }, []); // Empty dependency array - run only once

  // Other monitoring - separate useEffect
  useEffect(() => {
    try {
      // Monitor memory usage every 30 seconds
      const memoryInterval = setInterval(() => {
        monitorMemoryUsage();
      }, 30000);

      // Track previous network status to avoid duplicate notifications
      let previousOnlineStatus = navigator.onLine;
      let hasShownInitialStatus = false;

      // Monitor network status
      const cleanupNetworkMonitor = monitorNetworkStatus((status) => {
        actions.setNetworkStatus(status);

        // Only show notifications when status actually changes and notifications are enabled
        if (enableNetworkNotifications && hasShownInitialStatus && previousOnlineStatus !== status.online) {
          if (!status.online) {
            actions.addNotification({
              type: 'warning',
              message: 'You are currently offline. Some features may not work properly.',
              duration: 10000
            });
          } else {
            actions.addNotification({
              type: 'success',
              message: 'Connection restored!',
              duration: 3000
            });
          }
        }

        previousOnlineStatus = status.online;
        hasShownInitialStatus = true;
      });

      // Monitor page visibility
      const handleVisibilityChange = () => {
        if (document.hidden) {
          console.log('Page hidden - pausing non-critical operations');
        } else {
          console.log('Page visible - resuming operations');
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      // Cleanup
      return () => {
        clearInterval(memoryInterval);
        if (cleanupNetworkMonitor) cleanupNetworkMonitor();
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    } catch (error) {
      console.error('Performance monitoring error:', error);
      // Return empty cleanup function
      return () => {};
    }
  }, [actions, enableNetworkNotifications]);

  // This component doesn't render anything
  return null;
};

export default PerformanceMonitor;
