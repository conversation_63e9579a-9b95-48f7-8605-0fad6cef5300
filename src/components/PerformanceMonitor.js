import { useEffect } from 'react';
import { reportWebVitals, monitorMemoryUsage, monitorNetworkStatus } from '../utils/performance';
import { useApp } from '../context/AppContext';

const PerformanceMonitor = () => {
  const { actions } = useApp();

  useEffect(() => {
    try {
      // Monitor Web Vitals
      reportWebVitals((metric) => {
        console.log('Web Vital:', metric);

        // Send to analytics service
        if (window.gtag) {
          window.gtag('event', metric.name, {
            event_category: 'Web Vitals',
            value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
            event_label: metric.id,
            non_interaction: true,
          });
        }
      });

    // Monitor memory usage every 30 seconds
    const memoryInterval = setInterval(() => {
      monitorMemoryUsage();
    }, 30000);

    // Monitor network status
    const cleanupNetworkMonitor = monitorNetworkStatus((status) => {
      actions.setNetworkStatus(status);

      if (!status.online) {
        actions.addNotification({
          type: 'warning',
          message: 'You are currently offline. Some features may not work properly.',
          duration: 10000
        });
      } else {
        actions.addNotification({
          type: 'success',
          message: 'Connection restored!',
          duration: 3000
        });
      }
    });

    // Monitor page visibility
    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log('Page hidden - pausing non-critical operations');
      } else {
        console.log('Page visible - resuming operations');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

      // Cleanup
      return () => {
        clearInterval(memoryInterval);
        if (cleanupNetworkMonitor) cleanupNetworkMonitor();
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    } catch (error) {
      console.error('Performance monitoring error:', error);
      // Return empty cleanup function
      return () => {};
    }
  }, [actions]);

  // This component doesn't render anything
  return null;
};

export default PerformanceMonitor;
