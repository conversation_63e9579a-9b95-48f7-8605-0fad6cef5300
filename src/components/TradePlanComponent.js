import React, { useState, useEffect, useMemo } from 'react';
import styled from 'styled-components';
import {
  Target,
  Plus,
  Save,
  Trash2,
  CheckCircle,
  Circle,
  Calendar,
  Clock,
  BarChart3,
  TrendingUp,
  Search,
  Filter,
  Edit,
  Copy,
  Download,
  Upload,
  Zap,
  X
} from 'lucide-react';

const Container = styled.div`
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  color: white;
`;

const PlanHeader = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: #1a1a1a;
  border-radius: 15px;
  border: 1px solid #333;
`;

const Heading = styled.h1`
  font-size: 3rem;
  margin: 0 0 1rem 0;
  color: white;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #ccc;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
`;

const PlanStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
`;

const StatCard = styled.div`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    background: #333;
    border-color: #555;
    transform: translateY(-2px);
  }

  .icon {
    margin-bottom: 0.5rem;
    color: white;
  }

  .label {
    font-size: 0.9rem;
    color: #ccc;
    margin-bottom: 0.5rem;
  }

  .value {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
  }
`;

const PlanSection = styled.div`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;

  h3 {
    color: white;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const InputGroup = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;

  @media (max-width: 600px) {
    flex-direction: column;
  }
`;

const Input = styled.input`
  flex: 1;
  padding: 0.75rem;
  background: #1a1a1a;
  border: 1px solid #555;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: white;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  }

  &::placeholder {
    color: #888;
  }
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  background: ${props => {
    if (props.variant === 'danger') return '#dc3545';
    if (props.variant === 'secondary') return '#6b7280';
    return 'white';
  }};
  color: ${props => {
    if (props.variant === 'danger') return 'white';
    if (props.variant === 'secondary') return 'white';
    return 'black';
  }};
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;

  &:hover {
    background: ${props => {
      if (props.variant === 'danger') return '#c82333';
      if (props.variant === 'secondary') return '#4b5563';
      return '#f0f0f0';
    }};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const TaskList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const TaskItem = styled.div`
  background: ${props => props.completed ? '#1a4d1a' : '#2a2a2a'};
  border: 1px solid ${props => props.completed ? '#2d5a2d' : '#444'};
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.completed ? '#1e5a1e' : '#333'};
    border-color: ${props => props.completed ? '#3d6a3d' : '#555'};
  }
`;

const TaskHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
`;

const TaskContent = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
`;

const TaskText = styled.span`
  color: ${props => props.completed ? '#ccc' : 'white'};
  text-decoration: ${props => props.completed ? 'line-through' : 'none'};
  flex: 1;
  font-size: 1rem;
`;

const TaskActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const CheckboxButton = styled.button`
  background: transparent;
  border: none;
  color: ${props => props.completed ? '#4ade80' : '#888'};
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: ${props => props.completed ? '#22c55e' : '#ccc'};
    background: rgba(255, 255, 255, 0.1);
  }
`;

const ActionButton = styled.button`
  background: transparent;
  border: none;
  color: #888;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
  }
`;

const SavedPlansList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const SavedPlanItem = styled.div`
  background: #1a1a1a;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;

  &:hover {
    background: #333;
    border-color: #666;
  }
`;

const PlanItemHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const PlanName = styled.h4`
  color: white;
  margin: 0;
  font-size: 1.1rem;
`;

const PlanMeta = styled.div`
  color: #888;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PlanActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 2rem;
  color: #888;

  .icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h3 {
    color: #ccc;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
  }

  p {
    color: #888;
    font-size: 1.1rem;
  }
`;

const ProgressBar = styled.div`
  background: #1a1a1a;
  border-radius: 8px;
  height: 8px;
  overflow: hidden;
  margin-top: 0.5rem;
`;

const ProgressFill = styled.div`
  background: linear-gradient(90deg, #4ade80, #22c55e);
  height: 100%;
  width: ${props => props.percentage}%;
  transition: width 0.3s ease;
`;

const SearchContainer = styled.div`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;

  h3 {
    color: white;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const SearchGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 1rem;
  align-items: end;
`;

const TradePlanComponent = () => {
  const [tasks, setTasks] = useState([]);
  const [newTask, setNewTask] = useState('');
  const [planName, setPlanName] = useState('');
  const [savedPlans, setSavedPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingTask, setEditingTask] = useState(null);
  const [editingText, setEditingText] = useState('');

  useEffect(() => {
    const plans = JSON.parse(localStorage.getItem('tradePlans')) || [];
    setSavedPlans(plans);
  }, []);

  useEffect(() => {
    if (savedPlans.length > 0) {
      localStorage.setItem('tradePlans', JSON.stringify(savedPlans));
    }
  }, [savedPlans]);

  // Memoized statistics
  const stats = useMemo(() => {
    const completedTasks = tasks.filter(task => task.completed).length;
    const totalTasks = tasks.length;
    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    return {
      totalTasks,
      completedTasks,
      pendingTasks: totalTasks - completedTasks,
      completionRate,
      totalPlans: savedPlans.length
    };
  }, [tasks, savedPlans]);

  // Filtered tasks
  const filteredTasks = useMemo(() => {
    return tasks.filter(task =>
      task.text.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [tasks, searchTerm]);

  const addTask = () => {
    if (newTask.trim() !== '') {
      const newTaskObj = {
        id: Date.now(),
        text: newTask.trim(),
        completed: false,
        createdAt: new Date().toISOString()
      };
      setTasks([...tasks, newTaskObj]);
      setNewTask('');
    }
  };

  const toggleTaskCompletion = (taskId) => {
    const updatedTasks = tasks.map((task) =>
      task.id === taskId ? { ...task, completed: !task.completed } : task
    );
    setTasks(updatedTasks);
  };

  const deleteTask = (taskId) => {
    const updatedTasks = tasks.filter((task) => task.id !== taskId);
    setTasks(updatedTasks);
  };

  const startEditingTask = (task) => {
    setEditingTask(task.id);
    setEditingText(task.text);
  };

  const saveEditingTask = () => {
    if (editingText.trim() !== '') {
      const updatedTasks = tasks.map((task) =>
        task.id === editingTask ? { ...task, text: editingText.trim() } : task
      );
      setTasks(updatedTasks);
    }
    setEditingTask(null);
    setEditingText('');
  };

  const cancelEditingTask = () => {
    setEditingTask(null);
    setEditingText('');
  };

  const duplicateTask = (task) => {
    const duplicatedTask = {
      ...task,
      id: Date.now(),
      text: `${task.text} (Copy)`,
      completed: false,
      createdAt: new Date().toISOString()
    };
    setTasks([...tasks, duplicatedTask]);
  };

  const savePlan = () => {
    if (planName.trim() === '') {
      return;
    }
    const planExists = savedPlans.some(plan => plan.name === planName.trim());
    if (planExists) {
      return;
    }
    const newPlan = {
      id: Date.now(),
      name: planName.trim(),
      tasks,
      createdAt: new Date().toISOString(),
      completedTasks: tasks.filter(t => t.completed).length,
      totalTasks: tasks.length
    };
    setSavedPlans([...savedPlans, newPlan]);
    setPlanName('');
    setTasks([]);
  };

  const loadPlan = (plan) => {
    setSelectedPlan(plan);
    setTasks(plan.tasks || []);
  };

  const deletePlan = (planId) => {
    const updatedPlans = savedPlans.filter((plan) => plan.id !== planId);
    setSavedPlans(updatedPlans);

    if (selectedPlan && selectedPlan.id === planId) {
      setSelectedPlan(null);
      setTasks([]);
    }
  };

  const resetTasks = () => {
    const updatedTasks = tasks.map(task => ({ ...task, completed: false }));
    setTasks(updatedTasks);
  };

  const clearAllTasks = () => {
    setTasks([]);
  };

  const exportPlan = () => {
    const planData = {
      name: planName || 'Untitled Plan',
      tasks,
      exportedAt: new Date().toISOString()
    };
    const dataStr = JSON.stringify(planData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${planData.name}.json`;
    link.click();
  };

  return (
    <Container>
      {/* Plan Header */}
      <PlanHeader>
        <Heading>
          <Target size={40} />
          Trade Plan Manager
        </Heading>
        <Subtitle>
          Professional trading plan management - "Fail to plan, you plan to fail"
        </Subtitle>
      </PlanHeader>

      {/* Plan Statistics */}
      <PlanStats>
        <StatCard>
          <div className="icon">
            <Target size={24} />
          </div>
          <div className="label">Total Tasks</div>
          <div className="value">{stats.totalTasks}</div>
        </StatCard>
        <StatCard>
          <div className="icon">
            <CheckCircle size={24} />
          </div>
          <div className="label">Completed</div>
          <div className="value">{stats.completedTasks}</div>
        </StatCard>
        <StatCard>
          <div className="icon">
            <Clock size={24} />
          </div>
          <div className="label">Pending</div>
          <div className="value">{stats.pendingTasks}</div>
        </StatCard>
        <StatCard>
          <div className="icon">
            <TrendingUp size={24} />
          </div>
          <div className="label">Progress</div>
          <div className="value">{stats.completionRate}%</div>
        </StatCard>
        <StatCard>
          <div className="icon">
            <BarChart3 size={24} />
          </div>
          <div className="label">Saved Plans</div>
          <div className="value">{stats.totalPlans}</div>
        </StatCard>
      </PlanStats>

      {/* Add New Task */}
      <PlanSection>
        <h3>
          <Plus size={20} />
          Add New Task
        </h3>
        <InputGroup>
          <Input
            type="text"
            value={newTask}
            onChange={(e) => setNewTask(e.target.value)}
            placeholder="Enter your trading task..."
            onKeyPress={(e) => e.key === 'Enter' && addTask()}
          />
          <Button onClick={addTask} disabled={!newTask.trim()}>
            <Plus size={16} />
            Add Task
          </Button>
        </InputGroup>
      </PlanSection>

      {/* Search Tasks */}
      {tasks.length > 0 && (
        <SearchContainer>
          <h3>
            <Search size={20} />
            Search Tasks
          </h3>
          <SearchGrid>
            <Input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Button variant="secondary">
              <Filter size={16} />
              Filter
            </Button>
          </SearchGrid>
        </SearchContainer>
      )}

      {/* Task List */}
      <PlanSection>
        <h3>
          <Target size={20} />
          Current Tasks ({filteredTasks.length})
        </h3>

        {stats.totalTasks > 0 && (
          <div style={{ marginBottom: '1rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
              <span style={{ color: '#ccc', fontSize: '0.9rem' }}>
                Progress: {stats.completedTasks} of {stats.totalTasks} tasks completed
              </span>
              <span style={{ color: '#ccc', fontSize: '0.9rem' }}>
                {stats.completionRate}%
              </span>
            </div>
            <ProgressBar>
              <ProgressFill percentage={stats.completionRate} />
            </ProgressBar>
          </div>
        )}

        {filteredTasks.length === 0 ? (
          <EmptyState>
            <div className="icon">📋</div>
            <h3>{tasks.length === 0 ? 'No tasks yet' : 'No matching tasks'}</h3>
            <p>{tasks.length === 0 ? 'Start by adding your first trading task!' : 'Try adjusting your search terms'}</p>
          </EmptyState>
        ) : (
          <>
            <TaskList>
              {filteredTasks.map((task) => (
                <TaskItem key={task.id} completed={task.completed}>
                  <TaskHeader>
                    <TaskContent>
                      <CheckboxButton
                        completed={task.completed}
                        onClick={() => toggleTaskCompletion(task.id)}
                      >
                        {task.completed ? <CheckCircle size={20} /> : <Circle size={20} />}
                      </CheckboxButton>
                      {editingTask === task.id ? (
                        <Input
                          value={editingText}
                          onChange={(e) => setEditingText(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') saveEditingTask();
                            if (e.key === 'Escape') cancelEditingTask();
                          }}
                          style={{ margin: 0 }}
                        />
                      ) : (
                        <TaskText completed={task.completed}>{task.text}</TaskText>
                      )}
                    </TaskContent>
                    <TaskActions>
                      {editingTask === task.id ? (
                        <>
                          <Button onClick={saveEditingTask} style={{ padding: '0.25rem 0.5rem' }}>
                            <Save size={14} />
                          </Button>
                          <Button variant="secondary" onClick={cancelEditingTask} style={{ padding: '0.25rem 0.5rem' }}>
                            <X size={14} />
                          </Button>
                        </>
                      ) : (
                        <>
                          <ActionButton onClick={() => startEditingTask(task)}>
                            <Edit size={14} />
                          </ActionButton>
                          <ActionButton onClick={() => duplicateTask(task)}>
                            <Copy size={14} />
                          </ActionButton>
                          <ActionButton onClick={() => deleteTask(task.id)}>
                            <Trash2 size={14} />
                          </ActionButton>
                        </>
                      )}
                    </TaskActions>
                  </TaskHeader>
                </TaskItem>
              ))}
            </TaskList>

            {tasks.length > 0 && (
              <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem', flexWrap: 'wrap' }}>
                <Button variant="secondary" onClick={resetTasks}>
                  <Clock size={16} />
                  Reset All
                </Button>
                <Button variant="danger" onClick={clearAllTasks}>
                  <Trash2 size={16} />
                  Clear All
                </Button>
                <Button variant="secondary" onClick={exportPlan}>
                  <Download size={16} />
                  Export
                </Button>
              </div>
            )}
          </>
        )}
      </PlanSection>

      {/* Save Plan */}
      {tasks.length > 0 && (
        <PlanSection>
          <h3>
            <Save size={20} />
            Save Current Plan
          </h3>
          <InputGroup>
            <Input
              type="text"
              value={planName}
              onChange={(e) => setPlanName(e.target.value)}
              placeholder="Enter plan name..."
              onKeyPress={(e) => e.key === 'Enter' && savePlan()}
            />
            <Button onClick={savePlan} disabled={!planName.trim() || tasks.length === 0}>
              <Save size={16} />
              Save Plan
            </Button>
          </InputGroup>
        </PlanSection>
      )}

      {/* Saved Plans */}
      {savedPlans.length > 0 && (
        <PlanSection>
          <h3>
            <BarChart3 size={20} />
            Saved Trade Plans ({savedPlans.length})
          </h3>
          <SavedPlansList>
            {savedPlans.map((plan) => (
              <SavedPlanItem key={plan.id}>
                <PlanItemHeader>
                  <div>
                    <PlanName>{plan.name}</PlanName>
                    <PlanMeta>
                      <Calendar size={14} />
                      {new Date(plan.createdAt).toLocaleDateString()}
                      <Target size={14} />
                      {plan.totalTasks} tasks
                      <CheckCircle size={14} />
                      {plan.completedTasks} completed
                    </PlanMeta>
                    <ProgressBar>
                      <ProgressFill percentage={plan.totalTasks > 0 ? Math.round((plan.completedTasks / plan.totalTasks) * 100) : 0} />
                    </ProgressBar>
                  </div>
                  <PlanActions>
                    <Button variant="secondary" onClick={() => loadPlan(plan)}>
                      <Upload size={14} />
                      Load
                    </Button>
                    <Button variant="danger" onClick={() => deletePlan(plan.id)}>
                      <Trash2 size={14} />
                      Delete
                    </Button>
                  </PlanActions>
                </PlanItemHeader>
              </SavedPlanItem>
            ))}
          </SavedPlansList>
        </PlanSection>
      )}
    </Container>
  );
};

export default TradePlanComponent;
