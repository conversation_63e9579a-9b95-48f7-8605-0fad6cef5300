import React, { useState, useEffect, useMemo } from 'react';
import styled from 'styled-components';
import { 
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, 
  AreaChart, Area, ComposedChart, Bar, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hart, Pie, Cell,
  BarChart, ReferenceLine
} from 'recharts';
import { TrendingUp, TrendingDown, Target, DollarSign, Calendar, BarChart3, Download, AlertTriangle } from 'lucide-react';

const DashboardContainer = styled.div`
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  p {
    opacity: 0.8;
    font-size: 1.1rem;
  }
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const MetricCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }
`;

const MetricHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  
  h3 {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
`;

const MetricValue = styled.div`
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  
  &.positive { color: #4ade80; }
  &.negative { color: #f87171; }
  &.neutral { color: #fbbf24; }
`;

const MetricChange = styled.div`
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  opacity: 0.8;
  
  &.positive { color: #4ade80; }
  &.negative { color: #f87171; }
`;

const ChartContainer = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const ChartHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  
  h3 {
    margin: 0;
    font-size: 1.2rem;
  }
`;

const TimeframeSelector = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const TimeframeButton = styled.button`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  background: ${props => props.active ? 'rgba(255, 255, 255, 0.2)' : 'transparent'};
  color: white;
  cursor: pointer;
  transition: background 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const ExportButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(45deg, #4ade80, #22c55e);
  border: none;
  border-radius: 10px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
`;

const AlertsContainer = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const Alert = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(248, 113, 113, 0.2);
  border-radius: 10px;
  margin-bottom: 1rem;
  border-left: 4px solid #f87171;
`;

const AdvancedTradingDashboard = ({ trades = [] }) => {
  const [timeframe, setTimeframe] = useState('1M');
  const [selectedMetric, setSelectedMetric] = useState('pnl');

  // Advanced calculations
  const metrics = useMemo(() => {
    if (!trades.length) return {};

    const totalTrades = trades.length;
    const winningTrades = trades.filter(t => parseFloat(t.pnl) > 0);
    const losingTrades = trades.filter(t => parseFloat(t.pnl) < 0);
    
    const totalPnL = trades.reduce((sum, t) => sum + parseFloat(t.pnl || 0), 0);
    const totalWinPnL = winningTrades.reduce((sum, t) => sum + parseFloat(t.pnl || 0), 0);
    const totalLossPnL = Math.abs(losingTrades.reduce((sum, t) => sum + parseFloat(t.pnl || 0), 0));
    
    const winRate = (winningTrades.length / totalTrades) * 100;
    const profitFactor = totalLossPnL === 0 ? Infinity : totalWinPnL / totalLossPnL;
    const avgWin = winningTrades.length ? totalWinPnL / winningTrades.length : 0;
    const avgLoss = losingTrades.length ? totalLossPnL / losingTrades.length : 0;
    
    // Risk metrics
    const returns = trades.map(t => parseFloat(t.pnl || 0));
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    const volatility = Math.sqrt(variance);
    const sharpeRatio = volatility === 0 ? 0 : avgReturn / volatility;
    
    // Drawdown calculation
    let peak = 0;
    let maxDrawdown = 0;
    let runningPnL = 0;
    
    trades.forEach(trade => {
      runningPnL += parseFloat(trade.pnl || 0);
      if (runningPnL > peak) peak = runningPnL;
      const drawdown = (peak - runningPnL) / peak * 100;
      if (drawdown > maxDrawdown) maxDrawdown = drawdown;
    });

    return {
      totalPnL,
      winRate,
      profitFactor,
      avgWin,
      avgLoss,
      sharpeRatio,
      maxDrawdown,
      volatility,
      totalTrades,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length
    };
  }, [trades]);

  // Chart data preparation
  const chartData = useMemo(() => {
    let runningPnL = 0;
    return trades.map((trade, index) => {
      runningPnL += parseFloat(trade.pnl || 0);
      return {
        index: index + 1,
        pnl: parseFloat(trade.pnl || 0),
        cumulativePnL: runningPnL,
        date: trade.date,
        pair: trade.pair,
        winRate: ((trades.slice(0, index + 1).filter(t => parseFloat(t.pnl) > 0).length) / (index + 1)) * 100
      };
    });
  }, [trades]);

  const exportData = () => {
    const csvContent = [
      ['Date', 'Pair', 'Direction', 'PnL', 'Cumulative PnL', 'Win Rate'],
      ...chartData.map(row => [
        row.date,
        row.pair,
        row.pnl > 0 ? 'WIN' : 'LOSS',
        row.pnl,
        row.cumulativePnL,
        row.winRate.toFixed(2) + '%'
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `trading-data-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
  };

  return (
    <DashboardContainer>
      <Header>
        <h1>Advanced Trading Analytics</h1>
        <p>Professional-grade trading performance analysis</p>
      </Header>

      <MetricsGrid>
        <MetricCard>
          <MetricHeader>
            <h3>Total P&L</h3>
            <DollarSign size={20} />
          </MetricHeader>
          <MetricValue className={metrics.totalPnL > 0 ? 'positive' : metrics.totalPnL < 0 ? 'negative' : 'neutral'}>
            ${metrics.totalPnL?.toFixed(2) || '0.00'}
          </MetricValue>
          <MetricChange className={metrics.totalPnL > 0 ? 'positive' : 'negative'}>
            {metrics.totalPnL > 0 ? <TrendingUp size={16} /> : <TrendingDown size={16} />}
            {Math.abs(metrics.totalPnL || 0).toFixed(2)}
          </MetricChange>
        </MetricCard>

        <MetricCard>
          <MetricHeader>
            <h3>Win Rate</h3>
            <Target size={20} />
          </MetricHeader>
          <MetricValue className={metrics.winRate > 50 ? 'positive' : 'negative'}>
            {metrics.winRate?.toFixed(1) || '0.0'}%
          </MetricValue>
          <MetricChange>
            {metrics.winningTrades || 0} wins / {metrics.totalTrades || 0} trades
          </MetricChange>
        </MetricCard>

        <MetricCard>
          <MetricHeader>
            <h3>Profit Factor</h3>
            <BarChart3 size={20} />
          </MetricHeader>
          <MetricValue className={metrics.profitFactor > 1 ? 'positive' : 'negative'}>
            {metrics.profitFactor === Infinity ? '∞' : metrics.profitFactor?.toFixed(2) || '0.00'}
          </MetricValue>
          <MetricChange>
            Avg Win: ${metrics.avgWin?.toFixed(2) || '0.00'}
          </MetricChange>
        </MetricCard>

        <MetricCard>
          <MetricHeader>
            <h3>Sharpe Ratio</h3>
            <TrendingUp size={20} />
          </MetricHeader>
          <MetricValue className={metrics.sharpeRatio > 1 ? 'positive' : 'negative'}>
            {metrics.sharpeRatio?.toFixed(2) || '0.00'}
          </MetricValue>
          <MetricChange>
            Risk-adjusted return
          </MetricChange>
        </MetricCard>

        <MetricCard>
          <MetricHeader>
            <h3>Max Drawdown</h3>
            <TrendingDown size={20} />
          </MetricHeader>
          <MetricValue className="negative">
            {metrics.maxDrawdown?.toFixed(2) || '0.00'}%
          </MetricValue>
          <MetricChange>
            Peak to trough decline
          </MetricChange>
        </MetricCard>

        <MetricCard>
          <MetricHeader>
            <h3>Volatility</h3>
            <BarChart3 size={20} />
          </MetricHeader>
          <MetricValue className="neutral">
            {metrics.volatility?.toFixed(2) || '0.00'}
          </MetricValue>
          <MetricChange>
            Standard deviation
          </MetricChange>
        </MetricCard>
      </MetricsGrid>

      <ChartContainer>
        <ChartHeader>
          <h3>Cumulative P&L Performance</h3>
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <TimeframeSelector>
              {['1D', '1W', '1M', '3M', 'ALL'].map(tf => (
                <TimeframeButton
                  key={tf}
                  active={timeframe === tf}
                  onClick={() => setTimeframe(tf)}
                >
                  {tf}
                </TimeframeButton>
              ))}
            </TimeframeSelector>
            <ExportButton onClick={exportData}>
              <Download size={16} />
              Export Data
            </ExportButton>
          </div>
        </ChartHeader>
        <ResponsiveContainer width="100%" height={400}>
          <ComposedChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
            <XAxis dataKey="index" stroke="white" />
            <YAxis stroke="white" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(0,0,0,0.8)', 
                border: 'none', 
                borderRadius: '10px',
                color: 'white'
              }}
            />
            <Area 
              type="monotone" 
              dataKey="cumulativePnL" 
              fill="url(#colorPnL)" 
              stroke="#4ade80"
              strokeWidth={2}
            />
            <Bar dataKey="pnl" fill="rgba(255,255,255,0.3)" />
            <ReferenceLine y={0} stroke="white" strokeDasharray="2 2" />
            <defs>
              <linearGradient id="colorPnL" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#4ade80" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#4ade80" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
          </ComposedChart>
        </ResponsiveContainer>
      </ChartContainer>

      {metrics.maxDrawdown > 20 && (
        <AlertsContainer>
          <h3>Risk Alerts</h3>
          <Alert>
            <AlertTriangle size={20} />
            <div>
              <strong>High Drawdown Warning</strong>
              <p>Your maximum drawdown of {metrics.maxDrawdown?.toFixed(2)}% exceeds recommended levels. Consider reducing position sizes.</p>
            </div>
          </Alert>
        </AlertsContainer>
      )}
    </DashboardContainer>
  );
};

export default AdvancedTradingDashboard;
