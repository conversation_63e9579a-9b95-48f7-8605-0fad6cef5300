import React from 'react';
import { S<PERSON><PERSON>bar, Alert, Slide, IconButton } from '@mui/material';
import { useApp } from '../../context/AppContext';

const SlideTransition = (props) => {
  return <Slide {...props} direction="up" />;
};

const NotificationSystem = () => {
  const { state, actions } = useApp();
  const { notifications } = state;

  const handleClose = (id) => {
    actions.removeNotification(id);
  };

  return (
    <>
      {notifications.map((notification, index) => (
        <Snackbar
          key={notification.id}
          open={true}
          autoHideDuration={notification.duration || 6000}
          onClose={() => handleClose(notification.id)}
          TransitionComponent={SlideTransition}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          sx={{
            position: 'fixed',
            bottom: 16 + (index * 70), // Stack notifications
            right: 16,
            zIndex: 9999
          }}
        >
          <Alert
            severity={notification.type || 'info'}
            variant="filled"
            action={
              <IconButton
                size="small"
                aria-label="close"
                color="inherit"
                onClick={() => handleClose(notification.id)}
              >
                ×
              </IconButton>
            }
            sx={{ minWidth: 300 }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </>
  );
};

export default NotificationSystem;
