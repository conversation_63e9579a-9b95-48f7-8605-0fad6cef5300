import React from 'react';
import { Box, useTheme, useMediaQuery } from '@mui/material';

const ResponsiveContainer = ({ 
  children, 
  maxWidth = 'lg',
  padding = { xs: 2, sm: 3, md: 4 },
  ...props 
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box
      maxWidth={maxWidth}
      mx="auto"
      px={padding}
      width="100%"
      {...props}
      sx={{
        // Mobile-first responsive design
        display: 'flex',
        flexDirection: 'column',
        gap: { xs: 2, sm: 3, md: 4 },
        ...props.sx
      }}
    >
      {typeof children === 'function' 
        ? children({ isMobile, isTablet, theme })
        : children
      }
    </Box>
  );
};

export default ResponsiveContainer;
