import React from 'react';
import { Button } from '@mui/material';

const AccessibleButton = ({ 
  children, 
  ariaLabel, 
  ariaDescribedBy,
  loading = false,
  loadingText = 'Loading...',
  ...props 
}) => {
  return (
    <Button
      {...props}
      aria-label={ariaLabel || (typeof children === 'string' ? children : undefined)}
      aria-describedby={ariaDescribedBy}
      aria-busy={loading}
      disabled={loading || props.disabled}
      role="button"
      tabIndex={props.disabled ? -1 : 0}
    >
      {loading ? loadingText : children}
    </Button>
  );
};

export default AccessibleButton;
