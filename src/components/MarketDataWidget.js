import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { TrendingUp, TrendingDown, Activity, RefreshCw } from 'lucide-react';

const WidgetContainer = styled.div`
  background: #000000;
  border-radius: 15px;
  padding: 1.5rem;
  color: white;
  margin-bottom: 2rem;
  border: 1px solid #333;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const RefreshButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #333;
    border-color: #555;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const PairsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
`;

const PairCard = styled.div`
  background: #1a1a1a;
  border-radius: 10px;
  padding: 1rem;
  border: 1px solid #333;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(255, 255, 255, 0.1);
    border-color: #555;
  }
`;

const PairHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;

  h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: bold;
  }
`;

const Price = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
`;

const Change = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.9rem;

  &.positive { color: #4ade80; }
  &.negative { color: #f87171; }
`;

const Spread = styled.div`
  font-size: 0.8rem;
  opacity: 0.7;
  margin-top: 0.5rem;
`;

const LastUpdate = styled.div`
  text-align: center;
  margin-top: 1rem;
  font-size: 0.8rem;
  opacity: 0.7;
`;

const ConnectionStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;

  &.connected { color: #4ade80; }
  &.disconnected { color: #f87171; }
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.connected ? '#4ade80' : '#f87171'};
  animation: ${props => props.connected ? 'pulse 2s infinite' : 'none'};

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

// Mock market data - in production, this would connect to a real API
const MAJOR_PAIRS = [
  'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD', 'EURGBP'
];

const MarketDataWidget = () => {
  const [marketData, setMarketData] = useState({});
  const [isConnected, setIsConnected] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);
  const intervalRef = useRef(null);

  // Simulate real-time market data
  const generateMockData = () => {
    const data = {};
    MAJOR_PAIRS.forEach(pair => {
      const basePrice = {
        'EURUSD': 1.0850,
        'GBPUSD': 1.2650,
        'USDJPY': 149.50,
        'USDCHF': 0.8950,
        'AUDUSD': 0.6750,
        'USDCAD': 1.3450,
        'NZDUSD': 0.6150,
        'EURGBP': 0.8580
      }[pair] || 1.0000;

      const change = (Math.random() - 0.5) * 0.02; // ±1% change
      const price = basePrice + change;
      const changePercent = (change / basePrice) * 100;
      const spread = Math.random() * 0.0005 + 0.0001; // 0.1-0.6 pips

      data[pair] = {
        price: price.toFixed(pair.includes('JPY') ? 3 : 5),
        change: changePercent.toFixed(2),
        spread: spread.toFixed(5),
        bid: (price - spread/2).toFixed(pair.includes('JPY') ? 3 : 5),
        ask: (price + spread/2).toFixed(pair.includes('JPY') ? 3 : 5),
        timestamp: new Date().toISOString()
      };
    });
    return data;
  };

  const startDataStream = () => {
    setIsConnected(true);
    setMarketData(generateMockData());
    setLastUpdate(new Date());

    intervalRef.current = setInterval(() => {
      setMarketData(generateMockData());
      setLastUpdate(new Date());
    }, 2000); // Update every 2 seconds
  };

  const stopDataStream = () => {
    setIsConnected(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const refreshData = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
    setMarketData(generateMockData());
    setLastUpdate(new Date());
    setIsRefreshing(false);
  };

  useEffect(() => {
    startDataStream();
    return () => stopDataStream();
  }, []);

  return (
    <WidgetContainer>
      <Header>
        <h3>
          <Activity size={20} />
          Live Market Data
        </h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <ConnectionStatus className={isConnected ? 'connected' : 'disconnected'}>
            <StatusDot connected={isConnected} />
            {isConnected ? 'Connected' : 'Disconnected'}
          </ConnectionStatus>
          <RefreshButton onClick={refreshData} disabled={isRefreshing}>
            <RefreshCw size={16} style={{ animation: isRefreshing ? 'spin 1s linear infinite' : 'none' }} />
            Refresh
          </RefreshButton>
        </div>
      </Header>

      <PairsGrid>
        {MAJOR_PAIRS.map(pair => {
          const data = marketData[pair];
          if (!data) return null;

          const isPositive = parseFloat(data.change) >= 0;

          return (
            <PairCard key={pair}>
              <PairHeader>
                <h4>{pair}</h4>
                {isPositive ? <TrendingUp size={16} color="#4ade80" /> : <TrendingDown size={16} color="#f87171" />}
              </PairHeader>
              <Price>{data.price}</Price>
              <Change className={isPositive ? 'positive' : 'negative'}>
                {isPositive ? '+' : ''}{data.change}%
              </Change>
              <Spread>
                Bid: {data.bid} | Ask: {data.ask}
                <br />
                Spread: {data.spread}
              </Spread>
            </PairCard>
          );
        })}
      </PairsGrid>

      {lastUpdate && (
        <LastUpdate>
          Last updated: {lastUpdate.toLocaleTimeString()}
        </LastUpdate>
      )}

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </WidgetContainer>
  );
};

export default MarketDataWidget;
