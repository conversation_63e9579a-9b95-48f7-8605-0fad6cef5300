import React, { useState, useEffect, useMemo } from 'react';
import styled from 'styled-components';
import { collection, addDoc, getDocs, deleteDoc, doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '../firebase';
import ImageUpload from './ImageUpload';
import { Settings, Database, FileText, Users, BarChart3, Plus, Edit, Trash2, Save, X, Search, Filter } from 'lucide-react';

const Section = styled.section`
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  color: white;
`;

const AdminHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 3rem;
  padding: 2rem;
  background: #1a1a1a;
  border-radius: 15px;
  border: 1px solid #333;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const Heading = styled.h1`
  font-size: 2.5rem;
  margin: 0;
  color: white;
  font-weight: bold;
`;

const AdminStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
`;

const StatCard = styled.div`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    background: #333;
    border-color: #555;
    transform: translateY(-2px);
  }

  .icon {
    margin-bottom: 0.5rem;
    color: white;
  }

  .label {
    font-size: 0.9rem;
    color: #ccc;
    margin-bottom: 0.5rem;
  }

  .value {
    font-size: 2rem;
    font-weight: bold;
    color: white;
  }
`;

const TabContainer = styled.div`
  background: #1a1a1a;
  border-radius: 15px;
  padding: 1rem;
  margin-bottom: 2rem;
  border: 1px solid #333;
`;

const TabButtons = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const TabButton = styled.button`
  padding: 1rem 2rem;
  border: 1px solid #333;
  border-radius: 10px;
  background: ${props => props.active ? 'white' : '#2a2a2a'};
  color: ${props => props.active ? 'black' : 'white'};
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background: ${props => props.active ? '#f0f0f0' : '#333'};
    border-color: #555;
    transform: translateY(-2px);
  }
`;

const Form = styled.form`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;

  h3 {
    color: white;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  background: #1a1a1a;
  border: 1px solid #555;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: white;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  }

  &::placeholder {
    color: #888;
  }
`;

const TextArea = styled.textarea`
  padding: 0.75rem;
  background: #1a1a1a;
  border: 1px solid #555;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  resize: vertical;
  min-height: 100px;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: white;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  }

  &::placeholder {
    color: #888;
  }
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  background: ${props => props.variant === 'danger' ? '#ef4444' : props.variant === 'secondary' ? '#6b7280' : 'white'};
  color: ${props => props.variant === 'danger' ? 'white' : props.variant === 'secondary' ? 'white' : 'black'};
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background: ${props => props.variant === 'danger' ? '#dc2626' : props.variant === 'secondary' ? '#4b5563' : '#f0f0f0'};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const ElementsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const ElementCard = styled.div`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background: #333;
    border-color: #555;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const CardActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const CardImage = styled.img`
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #444;
`;

const ElementTitle = styled.h3`
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: white;
  font-weight: bold;
`;

const ElementDate = styled.p`
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const ElementContent = styled.p`
  font-size: 1rem;
  color: #ddd;
  margin-bottom: 1rem;
  line-height: 1.5;
`;

const ElementStockSymbol = styled.div`
  display: inline-block;
  background: #1a1a1a;
  border: 1px solid #555;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  color: white;
  font-weight: bold;
  margin-bottom: 1rem;
`;

const SearchContainer = styled.div`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;

  h3 {
    color: white;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const SearchGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 1rem;
  align-items: end;
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: #888;

  .icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h3 {
    color: #ccc;
    margin-bottom: 0.5rem;
  }

  p {
    color: #888;
  }
`;

const Admin = () => {
  // Data states
  const [data, setData] = useState([]);
  const [blogData, setBlogData] = useState([]);

  // Form states
  const [newElement, setNewElement] = useState({
    title: '',
    date: '',
    content: '',
    image: '',
    stockSymbol: ''
  });
  const [newBlog, setNewBlog] = useState({
    title: '',
    date: '',
    author: '',
    description: '',
    image: ''
  });

  // Edit states
  const [isEditingElement, setIsEditingElement] = useState(false);
  const [currentElementId, setCurrentElementId] = useState(null);
  const [isEditingBlog, setIsEditingBlog] = useState(false);
  const [currentBlogId, setCurrentBlogId] = useState(null);

  // UI states
  const [activeTab, setActiveTab] = useState('dashboard');
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'dashboard'));
      const dashboardData = [];
      querySnapshot.forEach((doc) => {
        dashboardData.push({ id: doc.id, ...doc.data() });
      });
      setData(dashboardData);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchBlogData = async () => {
    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'blogPosts'));
      const blogPosts = [];
      querySnapshot.forEach((doc) => {
        blogPosts.push({ id: doc.id, ...doc.data() });
      });
      setBlogData(blogPosts);
    } catch (error) {
      console.error('Error fetching blog data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    fetchBlogData();
  }, []);

  // Memoized statistics
  const stats = useMemo(() => {
    return {
      totalElements: data.length,
      totalBlogs: blogData.length,
      recentElements: data.filter(item => {
        const itemDate = new Date(item.date);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return itemDate >= weekAgo;
      }).length,
      recentBlogs: blogData.filter(item => {
        const itemDate = new Date(item.date);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return itemDate >= weekAgo;
      }).length
    };
  }, [data, blogData]);

  // Filtered data
  const filteredData = useMemo(() => {
    return data.filter(item => {
      const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.stockSymbol.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [data, searchTerm]);

  const filteredBlogs = useMemo(() => {
    return blogData.filter(item => {
      const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.author.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [blogData, searchTerm]);

  const handleInputChange = (e, setState, state) => {
    const { name, value } = e.target;
    setState({ ...state, [name]: value });
  };

  const handleImageUpload = (url, setState, state) => {
    setState({ ...state, image: url });
  };

  const handleAddOrUpdateElement = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      if (isEditingElement) {
        const docRef = doc(db, 'dashboard', currentElementId);
        const docSnapshot = await getDoc(docRef);
        if (docSnapshot.exists()) {
          await updateDoc(docRef, newElement);
        } else {
          console.error('Document does not exist:', currentElementId);
        }
        setIsEditingElement(false);
        setCurrentElementId(null);
      } else {
        await addDoc(collection(db, 'dashboard'), newElement);
      }
      setNewElement({
        title: '',
        date: '',
        content: '',
        image: '',
        stockSymbol: ''
      });
      fetchDashboardData();
    } catch (error) {
      console.error('Error adding or updating document:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditElement = (item) => {
    setNewElement(item);
    setIsEditingElement(true);
    setCurrentElementId(item.id);
  };

  const handleDeleteElement = async (id) => {
    try {
      const docRef = doc(db, 'dashboard', id);
      const docSnapshot = await getDoc(docRef);
      if (docSnapshot.exists()) {
        await deleteDoc(docRef);
      } else {
        console.error('Document does not exist:', id);
      }
      fetchDashboardData();
    } catch (error) {
      console.error('Error deleting document:', error);
    }
  };



  const handleAddOrUpdateBlog = async (e) => {
    e.preventDefault();
    try {
      if (isEditingBlog) {
        const docRef = doc(db, 'blogPosts', currentBlogId);
        const docSnapshot = await getDoc(docRef);
        if (docSnapshot.exists()) {
          await updateDoc(docRef, newBlog);
        } else {
          console.error('Document does not exist:', currentBlogId);
        }
        setIsEditingBlog(false);
        setCurrentBlogId(null);
      } else {
        await addDoc(collection(db, 'blogPosts'), newBlog);
      }
      setNewBlog({
        title: '',
        date: '',
        author: '',
        description: '',
        image: ''
      });
      fetchBlogData();
    } catch (error) {
      console.error('Error adding or updating document:', error);
    }
  };

  const handleEditBlog = (item) => {
    setNewBlog(item);
    setIsEditingBlog(true);
    setCurrentBlogId(item.id);
  };

  const handleDeleteBlog = async (id) => {
    try {
      const docRef = doc(db, 'blogPosts', id);
      const docSnapshot = await getDoc(docRef);
      if (docSnapshot.exists()) {
        await deleteDoc(docRef);
      } else {
        console.error('Document does not exist:', id);
      }
      fetchBlogData();
    } catch (error) {
      console.error('Error deleting document:', error);
    }
  };

  return (
    <Section>
      {/* Admin Header */}
      <AdminHeader>
        <HeaderLeft>
          <Settings size={32} />
          <Heading>Admin Dashboard</Heading>
        </HeaderLeft>
        <div style={{ color: '#ccc', fontSize: '0.9rem' }}>
          Welcome to the admin panel
        </div>
      </AdminHeader>

      {/* Statistics Cards */}
      <AdminStats>
        <StatCard>
          <div className="icon">
            <Database size={24} />
          </div>
          <div className="label">Total Elements</div>
          <div className="value">{stats.totalElements}</div>
        </StatCard>
        <StatCard>
          <div className="icon">
            <FileText size={24} />
          </div>
          <div className="label">Total Blogs</div>
          <div className="value">{stats.totalBlogs}</div>
        </StatCard>
        <StatCard>
          <div className="icon">
            <BarChart3 size={24} />
          </div>
          <div className="label">Recent Elements</div>
          <div className="value">{stats.recentElements}</div>
        </StatCard>
        <StatCard>
          <div className="icon">
            <Users size={24} />
          </div>
          <div className="label">Recent Blogs</div>
          <div className="value">{stats.recentBlogs}</div>
        </StatCard>
      </AdminStats>

      {/* Search and Filter */}
      <SearchContainer>
        <h3>
          <Search size={20} />
          Search & Filter
        </h3>
        <SearchGrid>
          <InputGroup>
            <Input
              type="text"
              placeholder="Search by title, content, or symbol..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
          <Button variant="secondary">
            <Filter size={16} />
            Filter
          </Button>
        </SearchGrid>
      </SearchContainer>

      {/* Tab Navigation */}
      <TabContainer>
        <TabButtons>
          <TabButton
            active={activeTab === 'dashboard'}
            onClick={() => setActiveTab('dashboard')}
          >
            <Database size={16} />
            Dashboard Elements
          </TabButton>
          <TabButton
            active={activeTab === 'blogs'}
            onClick={() => setActiveTab('blogs')}
          >
            <FileText size={16} />
            Blog Posts
          </TabButton>
        </TabButtons>

        {/* Dashboard Elements Tab */}
        {activeTab === 'dashboard' && (
          <>
            <Form onSubmit={handleAddOrUpdateElement}>
              <h3>
                <Plus size={20} />
                {isEditingElement ? 'Edit Dashboard Element' : 'Add New Dashboard Element'}
              </h3>
              <FormGrid>
                <InputGroup>
                  <Label>Title</Label>
                  <Input
                    type="text"
                    name="title"
                    placeholder="Enter title"
                    value={newElement.title}
                    onChange={(e) => handleInputChange(e, setNewElement, newElement)}
                  />
                </InputGroup>
                <InputGroup>
                  <Label>Date</Label>
                  <Input
                    type="date"
                    name="date"
                    value={newElement.date}
                    onChange={(e) => handleInputChange(e, setNewElement, newElement)}
                  />
                </InputGroup>
                <InputGroup>
                  <Label>Stock Symbol</Label>
                  <Input
                    type="text"
                    name="stockSymbol"
                    placeholder="e.g., AAPL, TSLA"
                    value={newElement.stockSymbol}
                    onChange={(e) => handleInputChange(e, setNewElement, newElement)}
                  />
                </InputGroup>
              </FormGrid>
              <InputGroup>
                <Label>Content</Label>
                <TextArea
                  name="content"
                  placeholder="Enter content description"
                  value={newElement.content}
                  onChange={(e) => handleInputChange(e, setNewElement, newElement)}
                />
              </InputGroup>
              <InputGroup>
                <Label>Image</Label>
                <ImageUpload onUpload={(url) => handleImageUpload(url, setNewElement, newElement)} />
              </InputGroup>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <Button type="submit" disabled={loading}>
                  {loading ? <LoadingSpinner /> : <Save size={16} />}
                  {isEditingElement ? 'Update Element' : 'Add Element'}
                </Button>
                {isEditingElement && (
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => {
                      setIsEditingElement(false);
                      setCurrentElementId(null);
                      setNewElement({
                        title: '',
                        date: '',
                        content: '',
                        image: '',
                        stockSymbol: ''
                      });
                    }}
                  >
                    <X size={16} />
                    Cancel
                  </Button>
                )}
              </div>
            </Form>

            <div style={{ marginBottom: '1rem' }}>
              <h3 style={{ color: 'white', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <Database size={20} />
                Dashboard Elements ({filteredData.length})
              </h3>
            </div>

            {filteredData.length === 0 ? (
              <EmptyState>
                <div className="icon">📊</div>
                <h3>No dashboard elements found</h3>
                <p>Add your first dashboard element using the form above</p>
              </EmptyState>
            ) : (
              <ElementsContainer>
                {filteredData.map((item) => (
                  <ElementCard key={item.id}>
                    <CardHeader>
                      <div>
                        <ElementTitle>{item.title}</ElementTitle>
                        <ElementDate>📅 {item.date}</ElementDate>
                      </div>
                      <CardActions>
                        <Button
                          variant="secondary"
                          onClick={() => handleEditElement(item)}
                          style={{ padding: '0.5rem' }}
                        >
                          <Edit size={14} />
                        </Button>
                        <Button
                          variant="danger"
                          onClick={() => handleDeleteElement(item.id)}
                          style={{ padding: '0.5rem' }}
                        >
                          <Trash2 size={14} />
                        </Button>
                      </CardActions>
                    </CardHeader>
                    {item.image && <CardImage src={item.image} alt={item.title} />}
                    <ElementContent>{item.content}</ElementContent>
                    <ElementStockSymbol>{item.stockSymbol}</ElementStockSymbol>
                  </ElementCard>
                ))}
              </ElementsContainer>
            )}
          </>
        )}

        {/* Blog Posts Tab */}
        {activeTab === 'blogs' && (
          <>
            <Form onSubmit={handleAddOrUpdateBlog}>
              <h3>
                <Plus size={20} />
                {isEditingBlog ? 'Edit Blog Post' : 'Add New Blog Post'}
              </h3>
              <FormGrid>
                <InputGroup>
                  <Label>Title</Label>
                  <Input
                    type="text"
                    name="title"
                    placeholder="Enter blog title"
                    value={newBlog.title}
                    onChange={(e) => handleInputChange(e, setNewBlog, newBlog)}
                  />
                </InputGroup>
                <InputGroup>
                  <Label>Date</Label>
                  <Input
                    type="date"
                    name="date"
                    value={newBlog.date}
                    onChange={(e) => handleInputChange(e, setNewBlog, newBlog)}
                  />
                </InputGroup>
                <InputGroup>
                  <Label>Author</Label>
                  <Input
                    type="text"
                    name="author"
                    placeholder="Author name"
                    value={newBlog.author}
                    onChange={(e) => handleInputChange(e, setNewBlog, newBlog)}
                  />
                </InputGroup>
              </FormGrid>
              <InputGroup>
                <Label>Description</Label>
                <TextArea
                  name="description"
                  placeholder="Enter blog description"
                  value={newBlog.description}
                  onChange={(e) => handleInputChange(e, setNewBlog, newBlog)}
                />
              </InputGroup>
              <InputGroup>
                <Label>Image</Label>
                <ImageUpload onUpload={(url) => handleImageUpload(url, setNewBlog, newBlog)} />
              </InputGroup>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <Button type="submit" disabled={loading}>
                  {loading ? <LoadingSpinner /> : <Save size={16} />}
                  {isEditingBlog ? 'Update Blog' : 'Add Blog'}
                </Button>
                {isEditingBlog && (
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => {
                      setIsEditingBlog(false);
                      setCurrentBlogId(null);
                      setNewBlog({
                        title: '',
                        date: '',
                        author: '',
                        description: '',
                        image: ''
                      });
                    }}
                  >
                    <X size={16} />
                    Cancel
                  </Button>
                )}
              </div>
            </Form>

            <div style={{ marginBottom: '1rem' }}>
              <h3 style={{ color: 'white', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <FileText size={20} />
                Blog Posts ({filteredBlogs.length})
              </h3>
            </div>

            {filteredBlogs.length === 0 ? (
              <EmptyState>
                <div className="icon">📝</div>
                <h3>No blog posts found</h3>
                <p>Add your first blog post using the form above</p>
              </EmptyState>
            ) : (
              <ElementsContainer>
                {filteredBlogs.map((item) => (
                  <ElementCard key={item.id}>
                    <CardHeader>
                      <div>
                        <ElementTitle>{item.title}</ElementTitle>
                        <ElementDate>📅 {item.date}</ElementDate>
                      </div>
                      <CardActions>
                        <Button
                          variant="secondary"
                          onClick={() => handleEditBlog(item)}
                          style={{ padding: '0.5rem' }}
                        >
                          <Edit size={14} />
                        </Button>
                        <Button
                          variant="danger"
                          onClick={() => handleDeleteBlog(item.id)}
                          style={{ padding: '0.5rem' }}
                        >
                          <Trash2 size={14} />
                        </Button>
                      </CardActions>
                    </CardHeader>
                    {item.image && <CardImage src={item.image} alt={item.title} />}
                    <ElementContent>{item.description}</ElementContent>
                    <ElementStockSymbol>By {item.author}</ElementStockSymbol>
                  </ElementCard>
                ))}
              </ElementsContainer>
            )}
          </>
        )}
      </TabContainer>
    </Section>
  );
};

export default Admin;
