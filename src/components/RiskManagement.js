import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { <PERSON>, AlertTriangle, Calculator as CalculatorIcon, TrendingDown, Target } from 'lucide-react';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';

const RiskContainer = styled.div`
  background: #000000;
  border-radius: 15px;
  padding: 2rem;
  color: white;
  margin-bottom: 2rem;
  border: 1px solid #333;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 2rem;

  h2 {
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
`;

const RiskGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
`;

const RiskCard = styled.div`
  background: #1a1a1a;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #333;
`;

const CalculatorContainer = styled.div`
  background: #1a1a1a;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #333;
`;

const InputGroup = styled.div`
  margin-bottom: 1rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
  }

  input {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 1rem;
  }
`;

const ResultCard = styled.div`
  background: #2a2a2a;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  border: 1px solid #444;

  h4 {
    margin: 0 0 0.5rem 0;
    color: #fff;
  }

  .value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #fff;
  }
`;

const AlertBox = styled.div`
  background: #2a2a2a;
  border-left: 4px solid #fbbf24;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid #444;
`;

const COLORS = ['#4ade80', '#f87171', '#fbbf24', '#60a5fa', '#a78bfa'];

const RiskManagement = ({ trades = [], accountBalance = 10000 }) => {
  const [positionSize, setPositionSize] = useState('');
  const [entryPrice, setEntryPrice] = useState('');
  const [stopLoss, setStopLoss] = useState('');
  const [takeProfit, setTakeProfit] = useState('');
  const [riskPercent, setRiskPercent] = useState('2');

  // Risk calculations
  const riskMetrics = useMemo(() => {
    if (!trades.length) return {};

    const returns = trades.map(t => parseFloat(t.pnl || 0));
    const totalPnL = returns.reduce((sum, pnl) => sum + pnl, 0);

    // Value at Risk (VaR) calculation - 95% confidence
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const varIndex = Math.floor(sortedReturns.length * 0.05);
    const var95 = sortedReturns[varIndex] || 0;

    // Maximum consecutive losses
    let maxConsecutiveLosses = 0;
    let currentLosses = 0;

    trades.forEach(trade => {
      if (parseFloat(trade.pnl || 0) < 0) {
        currentLosses++;
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLosses);
      } else {
        currentLosses = 0;
      }
    });

    // Risk distribution by pair
    const pairRisk = {};
    trades.forEach(trade => {
      const pair = trade.pair || 'Unknown';
      if (!pairRisk[pair]) pairRisk[pair] = { wins: 0, losses: 0, totalPnL: 0 };

      const pnl = parseFloat(trade.pnl || 0);
      pairRisk[pair].totalPnL += pnl;
      if (pnl > 0) pairRisk[pair].wins++;
      else if (pnl < 0) pairRisk[pair].losses++;
    });

    const riskDistribution = Object.entries(pairRisk).map(([pair, data]) => ({
      name: pair,
      value: Math.abs(data.totalPnL),
      wins: data.wins,
      losses: data.losses
    }));

    // Risk of ruin calculation (simplified)
    const winRate = trades.filter(t => parseFloat(t.pnl || 0) > 0).length / trades.length;
    const avgWin = trades.filter(t => parseFloat(t.pnl || 0) > 0)
      .reduce((sum, t) => sum + parseFloat(t.pnl || 0), 0) /
      trades.filter(t => parseFloat(t.pnl || 0) > 0).length || 0;
    const avgLoss = Math.abs(trades.filter(t => parseFloat(t.pnl || 0) < 0)
      .reduce((sum, t) => sum + parseFloat(t.pnl || 0), 0) /
      trades.filter(t => parseFloat(t.pnl || 0) < 0).length) || 0;

    const riskOfRuin = avgLoss > 0 ? Math.pow((avgLoss / (avgWin + avgLoss)), 100) * 100 : 0;

    return {
      var95,
      maxConsecutiveLosses,
      riskDistribution,
      riskOfRuin,
      totalPnL,
      winRate: winRate * 100
    };
  }, [trades]);

  // Position size calculator
  const calculatePosition = () => {
    const entry = parseFloat(entryPrice);
    const stop = parseFloat(stopLoss);
    const risk = parseFloat(riskPercent);

    if (!entry || !stop || !risk) return null;

    const riskAmount = (accountBalance * risk) / 100;
    const pipValue = Math.abs(entry - stop);
    const calculatedSize = riskAmount / pipValue;

    const profit = takeProfit ? parseFloat(takeProfit) : null;
    const riskReward = profit ? Math.abs(profit - entry) / Math.abs(entry - stop) : null;

    return {
      positionSize: calculatedSize.toFixed(2),
      riskAmount: riskAmount.toFixed(2),
      pipValue: pipValue.toFixed(5),
      riskReward: riskReward?.toFixed(2),
      potentialProfit: profit ? (calculatedSize * Math.abs(profit - entry)).toFixed(2) : null
    };
  };

  const positionCalc = calculatePosition();

  return (
    <RiskContainer>
      <Header>
        <h2>
          <Shield size={24} />
          Risk Management Center
        </h2>
        <p>Protect your capital with advanced risk analysis</p>
      </Header>

      <CalculatorContainer>
        <h3>
          <CalculatorIcon size={20} />
          Position Size Calculator
        </h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
          <InputGroup>
            <label>Account Balance ($)</label>
            <input
              type="number"
              value={accountBalance}
              readOnly
              style={{ background: 'rgba(255,255,255,0.5)' }}
            />
          </InputGroup>
          <InputGroup>
            <label>Risk Percentage (%)</label>
            <input
              type="number"
              value={riskPercent}
              onChange={(e) => setRiskPercent(e.target.value)}
              placeholder="2"
            />
          </InputGroup>
          <InputGroup>
            <label>Entry Price</label>
            <input
              type="number"
              value={entryPrice}
              onChange={(e) => setEntryPrice(e.target.value)}
              placeholder="1.0850"
              step="0.00001"
            />
          </InputGroup>
          <InputGroup>
            <label>Stop Loss</label>
            <input
              type="number"
              value={stopLoss}
              onChange={(e) => setStopLoss(e.target.value)}
              placeholder="1.0800"
              step="0.00001"
            />
          </InputGroup>
          <InputGroup>
            <label>Take Profit (Optional)</label>
            <input
              type="number"
              value={takeProfit}
              onChange={(e) => setTakeProfit(e.target.value)}
              placeholder="1.0950"
              step="0.00001"
            />
          </InputGroup>
        </div>

        {positionCalc && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
            <ResultCard>
              <h4>Position Size</h4>
              <div className="value">{positionCalc.positionSize} units</div>
            </ResultCard>
            <ResultCard>
              <h4>Risk Amount</h4>
              <div className="value">${positionCalc.riskAmount}</div>
            </ResultCard>
            {positionCalc.riskReward && (
              <ResultCard>
                <h4>Risk:Reward</h4>
                <div className="value">1:{positionCalc.riskReward}</div>
              </ResultCard>
            )}
            {positionCalc.potentialProfit && (
              <ResultCard>
                <h4>Potential Profit</h4>
                <div className="value">${positionCalc.potentialProfit}</div>
              </ResultCard>
            )}
          </div>
        )}
      </CalculatorContainer>

      <RiskGrid>
        <RiskCard>
          <h3>
            <TrendingDown size={20} />
            Risk Metrics
          </h3>
          <div style={{ marginBottom: '1rem' }}>
            <strong>Value at Risk (95%):</strong> ${riskMetrics.var95?.toFixed(2) || '0.00'}
          </div>
          <div style={{ marginBottom: '1rem' }}>
            <strong>Max Consecutive Losses:</strong> {riskMetrics.maxConsecutiveLosses || 0}
          </div>
          <div style={{ marginBottom: '1rem' }}>
            <strong>Risk of Ruin:</strong> {riskMetrics.riskOfRuin?.toFixed(2) || '0.00'}%
          </div>

          {riskMetrics.riskOfRuin > 10 && (
            <AlertBox>
              <AlertTriangle size={20} />
              <div>
                <strong>High Risk Warning!</strong>
                <br />
                Your risk of ruin is above 10%. Consider reducing position sizes.
              </div>
            </AlertBox>
          )}
        </RiskCard>

        <RiskCard>
          <h3>Risk Distribution by Pair</h3>
          <ResponsiveContainer width="100%" height={200}>
            <PieChart>
              <Pie
                data={riskMetrics.riskDistribution}
                cx="50%"
                cy="50%"
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {riskMetrics.riskDistribution?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </RiskCard>
      </RiskGrid>

      <AlertBox>
        <Target size={20} />
        <div>
          <strong>Risk Management Tips:</strong>
          <br />
          • Never risk more than 2% of your account per trade
          • Maintain a risk:reward ratio of at least 1:2
          • Use stop losses on every trade
          • Diversify across different currency pairs
        </div>
      </AlertBox>
    </RiskContainer>
  );
};

export default RiskManagement;
