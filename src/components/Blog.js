import React, { useEffect, useState, useMemo } from 'react';
import styled from 'styled-components';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '../firebase';
import { Helmet<PERSON>rovider, Helmet } from 'react-helmet-async';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { BookOpen, Calendar, User, Search, Filter, Clock, Tag, ArrowRight, X, Eye, Heart, Share2 } from 'lucide-react';

const Section = styled.section`
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  color: white;
`;

const BlogHeader = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: #1a1a1a;
  border-radius: 15px;
  border: 1px solid #333;
`;

const Heading = styled.h1`
  font-size: 3rem;
  margin: 0 0 1rem 0;
  color: white;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #ccc;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
`;

const BlogStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
`;

const StatCard = styled.div`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    background: #333;
    border-color: #555;
    transform: translateY(-2px);
  }

  .icon {
    margin-bottom: 0.5rem;
    color: white;
  }

  .label {
    font-size: 0.9rem;
    color: #ccc;
    margin-bottom: 0.5rem;
  }

  .value {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
  }
`;

const SearchContainer = styled.div`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;

  h3 {
    color: white;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const SearchGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 1rem;
  align-items: end;
`;

const Input = styled.input`
  padding: 0.75rem;
  background: #1a1a1a;
  border: 1px solid #555;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: white;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  }

  &::placeholder {
    color: #888;
  }
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  background: ${props => props.variant === 'secondary' ? '#6b7280' : 'white'};
  color: ${props => props.variant === 'secondary' ? 'white' : 'black'};
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background: ${props => props.variant === 'secondary' ? '#4b5563' : '#f0f0f0'};
    transform: translateY(-1px);
  }
`;

const BlogContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
`;

const BlogCard = styled.div`
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    background: #333;
    border-color: #555;
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  @media (max-width: 600px) {
    grid-template-columns: 1fr;
  }
`;

const BlogImage = styled(LazyLoadImage)`
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: all 0.3s ease;
`;

const BlogContent = styled.div`
  padding: 1.5rem;
`;

const BlogCardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const BlogMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const BlogTitle = styled.h2`
  font-size: 1.4rem;
  margin: 0 0 0.5rem 0;
  color: white;
  font-weight: bold;
  line-height: 1.3;
`;

const BlogDate = styled.div`
  font-size: 0.9rem;
  color: #ccc;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const BlogAuthor = styled.div`
  font-size: 0.9rem;
  color: #ccc;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const BlogDescription = styled.p`
  font-size: 1rem;
  color: #ddd;
  line-height: 1.6;
  margin: 1rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const BlogFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #444;
`;

const ReadMoreLink = styled.div`
  color: white;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;

  &:hover {
    color: #ccc;
  }
`;

const BlogActions = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const ActionButton = styled.button`
  background: transparent;
  border: none;
  color: #888;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  transition: all 0.3s ease;

  &:hover {
    color: white;
  }
`;

const CategoryTag = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const Modal = styled.div`
  display: ${(props) => (props.show ? 'flex' : 'none')};
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  backdrop-filter: blur(10px);
`;

const ModalContent = styled.div`
  background: #1a1a1a;
  border: 1px solid #444;
  border-radius: 15px;
  padding: 2rem;
  max-width: 800px;
  max-height: 90vh;
  width: 100%;
  overflow-y: auto;
  position: relative;
  color: white;

  @media (max-width: 600px) {
    margin: 1rem;
    padding: 1.5rem;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #333;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 3px;

    &:hover {
      background: #666;
    }
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #333;
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background: #555;
    transform: scale(1.1);
  }
`;

const ModalImage = styled(LazyLoadImage)`
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  border-radius: 12px;
  margin-bottom: 2rem;
  border: 1px solid #444;
`;

const ModalHeader = styled.div`
  margin-bottom: 2rem;

  h1 {
    color: white;
    font-size: 2rem;
    margin-bottom: 1rem;
    line-height: 1.3;
  }
`;

const ModalMeta = styled.div`
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ccc;
  font-size: 0.9rem;
`;

const ModalDescription = styled.div`
  color: #ddd;
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 2rem;
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin: 2rem auto;

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #ccc;

  p {
    margin-top: 1rem;
    font-size: 1.1rem;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 4rem 2rem;
  color: #888;

  .icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h3 {
    color: #ccc;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
  }

  p {
    color: #888;
    font-size: 1.1rem;
  }
`;

const Blog = () => {
  const [blogs, setBlogs] = useState([]);
  const [selectedBlog, setSelectedBlog] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('date');

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        const querySnapshot = await getDocs(collection(db, 'blogPosts'));
        const blogPosts = [];
        querySnapshot.forEach((doc) => {
          blogPosts.push({ id: doc.id, ...doc.data() });
        });
        // Sort by date (newest first) by default
        blogPosts.sort((a, b) => new Date(b.date) - new Date(a.date));
        setBlogs(blogPosts);
      } catch (error) {
        console.error('Error fetching blog posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPosts();
  }, []);

  // Memoized statistics
  const stats = useMemo(() => {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    return {
      totalBlogs: blogs.length,
      thisMonth: blogs.filter(blog => new Date(blog.date) >= thisMonth).length,
      lastMonth: blogs.filter(blog => {
        const blogDate = new Date(blog.date);
        return blogDate >= lastMonth && blogDate < thisMonth;
      }).length,
      authors: [...new Set(blogs.map(blog => blog.author))].length
    };
  }, [blogs]);

  // Filtered and sorted blogs
  const filteredBlogs = useMemo(() => {
    let filtered = blogs.filter(blog =>
      blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      blog.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      blog.author.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort blogs
    switch (sortBy) {
      case 'title':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'author':
        filtered.sort((a, b) => a.author.localeCompare(b.author));
        break;
      case 'date':
      default:
        filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
        break;
    }

    return filtered;
  }, [blogs, searchTerm, sortBy]);

  const handleBlogClick = (blog) => {
    setSelectedBlog(blog);
  };

  const handleCloseModal = () => {
    setSelectedBlog(null);
  };

  const truncateText = (text, length) => {
    return text.length > length ? text.substring(0, length) + '...' : text;
  };

  return (
    <HelmetProvider>
      <Helmet>
        <title>Blog - MarketPlayMaker</title>
        <meta name="description" content="Read the latest trading insights, market analysis, and financial education content. Stay updated with professional trading knowledge." />
        <meta name="robots" content="index, follow" />
        <meta property="og:title" content="Blog - MarketPlayMaker"/>
        <meta property="og:description" content="Professional trading insights and market analysis blog." />
        <meta property="og:image" content={selectedBlog ? selectedBlog.image : ''} />
        <meta property="og:url" content={window.location.href} />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Blog - MarketPlayMaker" />
        <meta name="twitter:description" content="Professional trading insights and market analysis blog." />
        <meta name="twitter:image" content={selectedBlog ? selectedBlog.image : ''} />
      </Helmet>

      <Section>
        {/* Blog Header */}
        <BlogHeader>
          <Heading>
            <BookOpen size={40} />
            Trading Insights Blog
          </Heading>
          <Subtitle>
            Professional market analysis, trading strategies, and financial education content
          </Subtitle>
        </BlogHeader>

        {/* Blog Statistics */}
        <BlogStats>
          <StatCard>
            <div className="icon">
              <BookOpen size={24} />
            </div>
            <div className="label">Total Articles</div>
            <div className="value">{stats.totalBlogs}</div>
          </StatCard>
          <StatCard>
            <div className="icon">
              <Calendar size={24} />
            </div>
            <div className="label">This Month</div>
            <div className="value">{stats.thisMonth}</div>
          </StatCard>
          <StatCard>
            <div className="icon">
              <User size={24} />
            </div>
            <div className="label">Authors</div>
            <div className="value">{stats.authors}</div>
          </StatCard>
          <StatCard>
            <div className="icon">
              <Clock size={24} />
            </div>
            <div className="label">Last Month</div>
            <div className="value">{stats.lastMonth}</div>
          </StatCard>
        </BlogStats>

        {/* Search and Filter */}
        <SearchContainer>
          <h3>
            <Search size={20} />
            Search & Filter Articles
          </h3>
          <SearchGrid>
            <Input
              type="text"
              placeholder="Search by title, content, or author..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              style={{
                padding: '0.75rem',
                background: '#1a1a1a',
                border: '1px solid #555',
                borderRadius: '8px',
                color: 'white',
                fontSize: '1rem'
              }}
            >
              <option value="date">Sort by Date</option>
              <option value="title">Sort by Title</option>
              <option value="author">Sort by Author</option>
            </select>
            <Button variant="secondary">
              <Filter size={16} />
              Filter
            </Button>
          </SearchGrid>
        </SearchContainer>

        {/* Blog Content */}
        {loading ? (
          <LoadingContainer>
            <LoadingSpinner />
            <p>Loading trading insights...</p>
          </LoadingContainer>
        ) : filteredBlogs.length === 0 ? (
          <EmptyState>
            <div className="icon">📝</div>
            <h3>No articles found</h3>
            <p>Try adjusting your search terms or check back later for new content</p>
          </EmptyState>
        ) : (
          <>
            <div style={{ marginBottom: '1rem', color: '#ccc', textAlign: 'center' }}>
              Showing {filteredBlogs.length} of {blogs.length} articles
            </div>
            <BlogContainer>
              {filteredBlogs.map((post) => (
                <BlogCard key={post.id} onClick={() => handleBlogClick(post)}>
                  <CategoryTag>
                    <Tag size={12} />
                    Trading
                  </CategoryTag>
                  {post.image && <BlogImage src={post.image} alt={post.title} />}
                  <BlogContent>
                    <BlogMeta>
                      <BlogTitle>{post.title}</BlogTitle>
                      <BlogDate>
                        <Calendar size={14} />
                        {new Date(post.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </BlogDate>
                      <BlogAuthor>
                        <User size={14} />
                        {post.author}
                      </BlogAuthor>
                    </BlogMeta>
                    <BlogDescription>{truncateText(post.description, 150)}</BlogDescription>
                    <BlogFooter>
                      <ReadMoreLink>
                        Read More
                        <ArrowRight size={16} />
                      </ReadMoreLink>
                      <BlogActions>
                        <ActionButton>
                          <Eye size={14} />
                          Read
                        </ActionButton>
                        <ActionButton>
                          <Heart size={14} />
                          Like
                        </ActionButton>
                        <ActionButton>
                          <Share2 size={14} />
                          Share
                        </ActionButton>
                      </BlogActions>
                    </BlogFooter>
                  </BlogContent>
                </BlogCard>
              ))}
            </BlogContainer>
          </>
        )}

        {/* Enhanced Modal */}
        {selectedBlog && (
          <Modal show={!!selectedBlog}>
            <ModalContent>
              <CloseButton onClick={handleCloseModal}>
                <X size={20} />
              </CloseButton>

              {selectedBlog.image && (
                <ModalImage src={selectedBlog.image} alt={selectedBlog.title} />
              )}

              <ModalHeader>
                <h1>{selectedBlog.title}</h1>
                <ModalMeta>
                  <MetaItem>
                    <Calendar size={16} />
                    {new Date(selectedBlog.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </MetaItem>
                  <MetaItem>
                    <User size={16} />
                    {selectedBlog.author}
                  </MetaItem>
                  <MetaItem>
                    <Clock size={16} />
                    {Math.ceil(selectedBlog.description.split(' ').length / 200)} min read
                  </MetaItem>
                  <MetaItem>
                    <Tag size={16} />
                    Trading Insights
                  </MetaItem>
                </ModalMeta>
              </ModalHeader>

              <ModalDescription>
                {selectedBlog.description.split('\n').map((paragraph, index) => (
                  <p key={index} style={{ marginBottom: '1rem' }}>
                    {paragraph}
                  </p>
                ))}
              </ModalDescription>

              <div style={{
                display: 'flex',
                gap: '1rem',
                justifyContent: 'center',
                paddingTop: '2rem',
                borderTop: '1px solid #444'
              }}>
                <Button>
                  <Heart size={16} />
                  Like Article
                </Button>
                <Button variant="secondary">
                  <Share2 size={16} />
                  Share
                </Button>
              </div>
            </ModalContent>
          </Modal>
        )}
      </Section>
    </HelmetProvider>
  );
};

export default Blog;
