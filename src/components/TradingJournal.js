import React, { useState, useEffect, useCallback, useMemo } from 'react';
import styled from 'styled-components';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';
import { collection, getDocs, addDoc, query, where, doc, deleteDoc, updateDoc } from 'firebase/firestore';
import { db, auth } from '../firebase';
import { useAuthState } from 'react-firebase-hooks/auth';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import AdvancedTradingDashboard from './AdvancedTradingDashboard';
import MarketDataWidget from './MarketDataWidget';
import RiskManagement from './RiskManagement';
import TradingAlerts from './TradingAlerts';
import EnhancedCalendar from './EnhancedCalendar';

// Styled components
const Section = styled.section`
  padding: 2rem;
  background: #000000;
  min-height: 100vh;
`;

const TabContainer = styled.div`
  background: #1a1a1a;
  border-radius: 15px;
  padding: 1rem;
  margin-bottom: 2rem;
  border: 1px solid #333;
`;

const TabButtons = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
  flex-wrap: wrap;
`;

const TabButton = styled.button`
  padding: 1rem 2rem;
  border: 1px solid #333;
  border-radius: 10px;
  background: ${props => props.active ? 'white' : '#2a2a2a'};
  color: ${props => props.active ? 'black' : 'white'};
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.active ? '#f0f0f0' : '#333'};
    transform: translateY(-2px);
    border-color: #555;
  }
`;

const TabContent = styled.div`
  display: ${props => props.active ? 'block' : 'none'};
`;

const StyledCalendar = styled(Calendar)`
  width: 100%;
  max-width: 400px;
  margin: 2rem auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: none;

  .react-calendar__tile {
    padding: 0.5rem 0.25rem;
    border-radius: 8px;
    &:hover {
      background-color:rgb(255, 255, 255);
    }
  }

  .react-calendar__tile--active {
    background-color: #000;
    color: #fff;
    border-radius: 8px;
  }
`;

const EntriesContainer = styled.div`
  margin-top: 1rem;
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
`;

const EntryCard = styled.div`
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background-color: #333;
    border-color: #555;
    transform: translateY(-2px);
  }
`;

const EntryField = styled.p`
  font-size: 0.8rem;
  margin: 0.25rem 0;
  color: white;
`;

const DashboardSection = styled.div`
  margin: 1rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const StatCard = styled.div`
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 1rem;
  margin: 0.5rem;
  width: 100%;
  max-width: 150px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  font-size: 0.8rem;
  transition: transform 0.3s, background-color 0.3s;
  cursor: pointer;
  color: white;

  h3 {
    color: white;
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
  }

  p {
    color: white;
    margin: 0;
    font-weight: bold;
    font-size: 1rem;
  }

  &:hover {
    transform: translateY(-10px);
    background-color: #333;
    border-color: #555;
  }
`;

const CardsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
  max-width: 400px;
  margin: 0 auto;
`;

const Input = styled.input`
  padding: 0.5rem;
  width: 100%;
  font-size: 0.8rem;
  border: 1px solid #ccc;
  border-radius: 4px;
`;

const Button = styled.button`
  padding: 0.5rem 1rem;
  background-color: #000;
  color: #fff;
  border: none;
  cursor: pointer;
  margin-top: 0.5rem;
  margin-right: 15px;
  margin-left: 15px;
  font-size: 0.8rem;
  border-radius: 4px;
  &:hover {
    background-color: #333;
  }
`;

const DeleteButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: red;
  color: #fff;
  border: none;
  cursor: pointer;
  margin-top: 0.5rem;
  margin-left: 15px;
  font-size: 0.8rem;
  border-radius: 4px;
  &:hover {
    background-color: darkred;
  }
`;

const EditButton = styled(Button)`
margin-left: 15px;
margin-right: 15px;
  background-color: #000; // Set the background color to black
  &:hover {
    background-color: #333; // Darker black for hover effect
  }
`;


// Modal styling
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: #1a1a1a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 2rem;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  color: white;

  h3 {
    color: white;
    margin-bottom: 1rem;
  }
`;

const ModalCloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: white;

  &:hover {
    color: #ccc;
  }
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  background: #ff4444;
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const ErrorCloseButton = styled.button`
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  margin-left: 1rem;
`;

const FormError = styled.div`
  color: #ff4444;
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
  z-index: 100;
`;

const LoadingText = styled.div`
  color: white;
  display: flex;
  align-items: center;
  font-size: 1.1rem;
`;

// Utility functions
const stripCurrencySymbol = (value) => value.replace(/[^0-9.-]+/g, '');

const formatDate = (date) => {
  if (isNaN(new Date(date))) {
    return new Date().toISOString().split('T')[0];
  }
  const d = new Date(date);
  const month = `${d.getMonth() + 1}`.padStart(2, '0');
  const day = `${d.getDate()}`.padStart(2, '0');
  const year = d.getFullYear();
  return `${year}-${month}-${day}`;
};

const FIELD_ORDER = [
  'pair',
  'direction',
  'date',
  'outcome',
  'pnl',
  'gain',
  'risk',
  'rrr',
  'entryTf',
  'entryWindow',
  'day',
  'model',
  'killzone',
  'timeInTrade'
];

const TradingJournal = () => {
  const [user] = useAuthState(auth);
  const [entries, setEntries] = useState([]);
  const [date, setDate] = useState(new Date());
  const [activeTab, setActiveTab] = useState('dashboard');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formErrors, setFormErrors] = useState({});
  const [journalEntry, setJournalEntry] = useState({
    pair: '',
    direction: '',
    date: '',
    outcome: '',
    pnl: '',
    gain: '',
    risk: '',
    rrr: '',
    entryTf: '',
    entryWindow: '',
    day: '',
    model: '',
    killzone: '',
    timeInTrade: ''
  });
  const [editMode, setEditMode] = useState(false);
  const [modalEntry, setModalEntry] = useState(null);

  const fetchEntries = useCallback(async () => {
    if (user) {
      setLoading(true);
      setError(null);
      try {
        const q = query(collection(db, 'tradingJournal'), where('userId', '==', user.uid));
        const querySnapshot = await getDocs(q);
        const fetchedEntries = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        setEntries(fetchedEntries);
      } catch (error) {
        console.error("Error fetching entries: ", error);
        setError("Failed to load trading entries. Please try again.");
      } finally {
        setLoading(false);
      }
    }
  }, [user]);

  useEffect(() => {
    fetchEntries();
  }, [fetchEntries, date]);

  const validateForm = () => {
    const errors = {};

    if (!journalEntry.pair.trim()) errors.pair = 'Currency pair is required';
    if (!journalEntry.direction.trim()) errors.direction = 'Direction is required';
    if (!journalEntry.date.trim()) errors.date = 'Date is required';
    if (!journalEntry.pnl.trim()) errors.pnl = 'P&L is required';
    else if (isNaN(parseFloat(journalEntry.pnl))) errors.pnl = 'P&L must be a valid number';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setJournalEntry({ ...journalEntry, [name]: value });

    // Clear specific field error when user starts typing
    if (formErrors[name]) {
      setFormErrors({ ...formErrors, [name]: '' });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (editMode) {
        const entryRef = doc(db, 'tradingJournal', journalEntry.id);
        await updateDoc(entryRef, { ...journalEntry });
        setEditMode(false);
      } else {
        await addDoc(collection(db, 'tradingJournal'), {
          ...journalEntry,
          userId: user.uid
        });
      }
      setJournalEntry({
        pair: '',
        direction: '',
        date: '',
        outcome: '',
        pnl: '',
        gain: '',
        risk: '',
        rrr: '',
        entryTf: '',
        entryWindow: '',
        day: '',
        model: '',
        killzone: '',
        timeInTrade: ''
      });
      setFormErrors({});
      fetchEntries();
    } catch (error) {
      console.error("Error submitting entry: ", error);
      setError("Failed to save trade entry. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleEditEntry = (entry) => {
    setJournalEntry(entry);
    setEditMode(true);
  };

  const handleDeleteEntry = async (id) => {
    try {
      await deleteDoc(doc(db, 'tradingJournal', id));
      fetchEntries();
    } catch (error) {
      console.error("Error deleting entry: ", error);
    }
  };


  const showModal = (entry) => {
    setModalEntry(entry);
  };

  const closeModal = () => {
    setModalEntry(null);
  };

  const stats = useMemo(() => {
    if (entries.length === 0) {
      return {
        totalPnl: 0,
        profitFactor: 0,
        avgWin: 0,
        avgLoss: 0,
        winRate: 0,
        dailyPnl: [{ date: formatDate(new Date()), pnl: 0, entries: [] }]
      };
    }

    let totalPnl = 0;
    let winningTrades = 0;
    let losingTrades = 0;
    let totalWinPnl = 0;
    let totalLossPnl = 0;
    let dailyPnl = {};

    entries.forEach(entry => {
      const pnlValue = parseFloat(stripCurrencySymbol(entry.pnl));
      if (!isNaN(pnlValue)) {
        totalPnl += pnlValue;
        if (pnlValue > 0) {
          winningTrades++;
          totalWinPnl += pnlValue;
        } else if (pnlValue < 0) {
          losingTrades++;
          totalLossPnl += pnlValue;
        }
        const entryDate = formatDate(entry.date);
        if (!dailyPnl[entryDate]) {
          dailyPnl[entryDate] = [];
        }
        dailyPnl[entryDate].push(pnlValue);
      }
    });

    const totalTrades = winningTrades + losingTrades;
    const winRate = totalTrades === 0 ? 0 : (winningTrades / totalTrades) * 100;
    const avgWin = winningTrades === 0 ? 0 : totalWinPnl / winningTrades;
    const avgLoss = losingTrades === 0 ? 0 : totalLossPnl / losingTrades;
    const profitFactor = totalLossPnl === 0 ? (totalWinPnl > 0 ? Infinity : 0) : totalWinPnl / Math.abs(totalLossPnl);

    return {
      totalPnl,
      profitFactor,
      avgWin,
      avgLoss,
      winRate,
      dailyPnl: Object.keys(dailyPnl).map(date => ({
        date,
        pnl: dailyPnl[date].reduce((acc, val) => acc + val, 0),
        entries: dailyPnl[date]
      }))
    };
  }, [entries]);

  const tileContent = ({ date }) => {
    const formattedDate = formatDate(date);
    const entriesOnDate = stats.dailyPnl.find(entry => entry.date === formattedDate);
    return (
      <div>
        {entriesOnDate?.entries.length > 0 ? (
          entriesOnDate.entries.map((pnl, index) => (
            <div key={index}>
              {`$${pnl.toFixed(2)}`}
            </div>
          ))
        ) : (
          <div></div>
        )}
      </div>
    );
  };

  const tabs = [
    { id: 'dashboard', label: '📊 Advanced Analytics', component: <AdvancedTradingDashboard trades={entries} /> },
    { id: 'market', label: '📈 Live Market Data', component: <MarketDataWidget /> },
    { id: 'alerts', label: '🔔 Trading Alerts', component: <TradingAlerts /> },
    { id: 'risk', label: '🛡️ Risk Management', component: <RiskManagement trades={entries} /> },
    { id: 'journal', label: '📝 Trading Journal', component: null }, // Will render the original journal
    { id: 'calendar', label: '📅 Calendar View', component: null } // Will render calendar
  ];

  return (
    <Section>
      {error && (
        <ErrorMessage>
          <span>{error}</span>
          <ErrorCloseButton onClick={() => setError(null)}>×</ErrorCloseButton>
        </ErrorMessage>
      )}

      <TabContainer style={{ position: 'relative' }}>
        {loading && (
          <LoadingOverlay>
            <LoadingText>
              <LoadingSpinner />
              Loading...
            </LoadingText>
          </LoadingOverlay>
        )}
        <TabButtons>
          {tabs.map(tab => (
            <TabButton
              key={tab.id}
              active={activeTab === tab.id}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </TabButton>
          ))}
        </TabButtons>

        {/* Advanced Analytics Tab */}
        <TabContent active={activeTab === 'dashboard'}>
          <AdvancedTradingDashboard trades={entries} />
        </TabContent>

        {/* Live Market Data Tab */}
        <TabContent active={activeTab === 'market'}>
          <MarketDataWidget />
        </TabContent>

        {/* Trading Alerts Tab */}
        <TabContent active={activeTab === 'alerts'}>
          <TradingAlerts />
        </TabContent>

        {/* Risk Management Tab */}
        <TabContent active={activeTab === 'risk'}>
          <RiskManagement trades={entries} />
        </TabContent>

        {/* Original Trading Journal Tab */}
        <TabContent active={activeTab === 'journal'}>
          <div style={{ background: '#1a1a1a', borderRadius: '15px', padding: '2rem', color: 'white', border: '1px solid #333' }}>
            <h2 style={{ textAlign: 'center', marginBottom: '2rem' }}>Trading Journal</h2>
            <CardsContainer>
        <StatCard>
          <h3>Total P&L</h3>
          <p>${stats.totalPnl.toFixed(2)}</p>
        </StatCard>
        <StatCard>
          <h3>Profit Factor</h3>
          <p>{stats.profitFactor === Infinity ? '∞' : stats.profitFactor.toFixed(2)}</p>
        </StatCard>
        <StatCard>
          <h3>Average Winning Trade</h3>
          <p>${stats.avgWin.toFixed(2)}</p>
        </StatCard>
        <StatCard>
          <h3>Average Losing Trade</h3>
          <p>${stats.avgLoss.toFixed(2)}</p>
        </StatCard>
        <StatCard>
          <h3>Win Rate</h3>
          <p>{stats.winRate.toFixed(2)}%</p>
        </StatCard>
      </CardsContainer>
      <DashboardSection>
        <h3 style={{ color: 'white', marginBottom: '1rem' }}>Daily Net Cumulative P&L</h3>
        <ResponsiveContainer width="100%" height={200}>
          <LineChart data={stats.dailyPnl}>
            <CartesianGrid strokeDasharray="3 3" stroke="#333" />
            <XAxis dataKey="date" stroke="white" />
            <YAxis stroke="white" />
            <Tooltip
              contentStyle={{
                backgroundColor: '#2a2a2a',
                border: '1px solid #444',
                borderRadius: '8px',
                color: 'white'
              }}
              content={({ payload }) => {
                if (payload && payload.length) {
                  return (
                    <div style={{ backgroundColor: '#2a2a2a', padding: '0.5rem', borderRadius: '8px', border: '1px solid #444' }}>
                      <p style={{ color: 'white', margin: 0 }}>{`Date: ${payload[0].payload.date}`}</p>
                      <p style={{ color: 'white', margin: 0 }}>{`PnL: $${payload[0].payload.pnl.toFixed(2)}`}</p>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Legend />
            <Line type="monotone" dataKey="pnl" stroke="#4ade80" strokeWidth={2} activeDot={{ r: 8 }} />
          </LineChart>
        </ResponsiveContainer>
      </DashboardSection>
      {/* Enhanced Calendar Integration */}
      <div style={{ marginBottom: '2rem' }}>
        <EnhancedCalendar
          trades={entries}
          onTradeSelect={showModal}
          onDateSelect={setDate}
        />
      </div>

      {/* All Entries List */}
      <EntriesContainer>
        <h3>All Entries ({entries.length} trades)</h3>
        {entries.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '2rem', opacity: 0.7 }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
            <h4>No trades yet</h4>
            <p>Add your first trade using the form below</p>
          </div>
        ) : (
          entries.map((entry, index) => (
            <EntryCard key={entry.id}>
              <EntryField># {index + 1}</EntryField>
              <EntryField>{`Pair: ${entry.pair}`}</EntryField>
              <EntryField>{`P&L: ${entry.pnl}`}</EntryField>
              <EntryField>{`Date: ${entry.date}`}</EntryField>
              <div style={{ display: 'flex', gap: '0.5rem', marginTop: '0.5rem' }}>
                <Button onClick={() => showModal(entry)}>
                  Show Details
                </Button>
                <EditButton onClick={() => handleEditEntry(entry)}>Edit</EditButton>
                <DeleteButton onClick={() => handleDeleteEntry(entry.id)}>Delete</DeleteButton>
              </div>
            </EntryCard>
          ))
        )}
      </EntriesContainer>
      {/* Add New Trade Form */}
      <div style={{ background: '#2a2a2a', borderRadius: '12px', padding: '1.5rem', marginBottom: '2rem', border: '1px solid #444' }}>
        <h3 style={{ marginBottom: '1rem', color: 'white' }}>
          {editMode ? 'Edit Trade' : 'Add New Trade'}
        </h3>
        <Form onSubmit={handleSubmit}>
          {FIELD_ORDER.map((field) => (
            field !== 'id' && field !== 'userId' && (
              <div key={field}>
                <Input
                  type={field === 'date' ? 'date' : 'text'}
                  name={field}
                  placeholder={field.replace(/([A-Z])/g, ' $1').toUpperCase()}
                  value={journalEntry[field]}
                  onChange={handleChange}
                  style={{
                    borderColor: formErrors[field] ? '#ff4444' : '#555',
                    background: '#1a1a1a',
                    color: 'white'
                  }}
                />
                {formErrors[field] && (
                  <FormError>{formErrors[field]}</FormError>
                )}
              </div>
            )
          ))}
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <LoadingSpinner />
                {editMode ? 'Updating...' : 'Adding...'}
              </>
            ) : (
              editMode ? 'Update Entry' : 'Add Entry'
            )}
          </Button>
        </Form>
      </div>
            </div>
        </TabContent>

        {/* Calendar View Tab */}
        <TabContent active={activeTab === 'calendar'}>
          <EnhancedCalendar
            trades={entries}
            onTradeSelect={showModal}
            onDateSelect={setDate}
          />
        </TabContent>
      </TabContainer>

      {/* Modal for showing details */}
      {modalEntry && (
        <ModalOverlay>
          <ModalContent>
            <ModalCloseButton onClick={closeModal}>×</ModalCloseButton>
            <h3>Entry Details</h3>
            {FIELD_ORDER.map((field) => (
              field !== 'id' && field !== 'userId' && (
                <EntryField key={field}>
                  <strong>{field.replace(/([A-Z])/g, ' $1').toUpperCase()}:</strong> {modalEntry[field]}
                </EntryField>
              )
            ))}
          </ModalContent>
        </ModalOverlay>
      )}
    </Section>
  );
};

export default TradingJournal;
