import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Initial state
const initialState = {
  user: null,
  trades: [],
  loading: false,
  error: null,
  notifications: [],
  preferences: {
    theme: 'light',
    currency: 'USD',
    timezone: 'UTC',
    notifications: true
  },
  networkStatus: { online: true }
};

// Action types
export const ActionTypes = {
  SET_USER: 'SET_USER',
  SET_TRADES: 'SET_TRADES',
  ADD_TRADE: 'ADD_TRADE',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',
  UPDATE_PREFERENCES: 'UPDATE_PREFERENCES',
  SET_NETWORK_STATUS: 'SET_NETWORK_STATUS',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Reducer
const appReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_USER:
      return { ...state, user: action.payload };

    case ActionTypes.SET_TRADES:
      return { ...state, trades: action.payload };

    case ActionTypes.ADD_TRADE:
      return { ...state, trades: [...state.trades, action.payload] };

    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };

    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false };

    case ActionTypes.CLEAR_ERROR:
      return { ...state, error: null };

    case ActionTypes.ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [...state.notifications, {
          id: Date.now(),
          ...action.payload
        }]
      };

    case ActionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload)
      };

    case ActionTypes.UPDATE_PREFERENCES:
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload }
      };

    case ActionTypes.SET_NETWORK_STATUS:
      return { ...state, networkStatus: action.payload };

    default:
      return state;
  }
};

// Context
const AppContext = createContext();

// Provider component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load preferences from localStorage
  useEffect(() => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const savedPreferences = localStorage.getItem('marketplaymaker-preferences');
        if (savedPreferences) {
          try {
            const preferences = JSON.parse(savedPreferences);
            dispatch({ type: ActionTypes.UPDATE_PREFERENCES, payload: preferences });
          } catch (error) {
            console.error('Failed to parse preferences:', error);
          }
        }
      }
    } catch (error) {
      console.error('Failed to access localStorage:', error);
    }
  }, []);

  // Save preferences to localStorage
  useEffect(() => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('marketplaymaker-preferences', JSON.stringify(state.preferences));
      }
    } catch (error) {
      console.error('Failed to save preferences:', error);
    }
  }, [state.preferences]);

  // Action creators
  const actions = {
    setUser: (user) => dispatch({ type: ActionTypes.SET_USER, payload: user }),
    setTrades: (trades) => dispatch({ type: ActionTypes.SET_TRADES, payload: trades }),
    addTrade: (trade) => dispatch({ type: ActionTypes.ADD_TRADE, payload: trade }),
    setLoading: (loading) => dispatch({ type: ActionTypes.SET_LOADING, payload: loading }),
    setError: (error) => dispatch({ type: ActionTypes.SET_ERROR, payload: error }),
    clearError: () => dispatch({ type: ActionTypes.CLEAR_ERROR }),
    addNotification: (notification) => dispatch({ type: ActionTypes.ADD_NOTIFICATION, payload: notification }),
    removeNotification: (id) => dispatch({ type: ActionTypes.REMOVE_NOTIFICATION, payload: id }),
    updatePreferences: (preferences) => dispatch({ type: ActionTypes.UPDATE_PREFERENCES, payload: preferences }),
    setNetworkStatus: (status) => dispatch({ type: ActionTypes.SET_NETWORK_STATUS, payload: status })
  };

  return (
    <AppContext.Provider value={{ state, actions }}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
