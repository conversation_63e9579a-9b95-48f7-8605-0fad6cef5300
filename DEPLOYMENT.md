# 🚀 MarketPlayMaker Deployment Guide

## Production Deployment Checklist

### **Environment Setup**
- [ ] Copy `.env.example` to `.env` and fill in production values
- [ ] Set up Firebase project and configure authentication
- [ ] Configure Stripe for payment processing
- [ ] Set up MetaApi account and obtain API tokens
- [ ] Configure domain and SSL certificates

### **Build & Deploy**
```bash
# Install dependencies
npm install

# Run tests
npm run test:ci

# Build for production
npm run build

# Deploy to hosting platform
npm run deploy
```

### **Performance Optimization**
- [ ] Enable gzip compression on server
- [ ] Configure CDN for static assets
- [ ] Set up proper caching headers
- [ ] Enable HTTP/2
- [ ] Optimize images and assets

### **Security Configuration**
- [ ] Configure security headers (see `public/_headers`)
- [ ] Set up Content Security Policy
- [ ] Enable HTTPS redirect
- [ ] Configure CORS properly
- [ ] Set up rate limiting

### **Monitoring & Analytics**
- [ ] Set up Google Analytics
- [ ] Configure error tracking (Sentry)
- [ ] Set up performance monitoring
- [ ] Configure uptime monitoring
- [ ] Set up log aggregation

### **SEO Optimization**
- [ ] Submit sitemap to search engines
- [ ] Configure robots.txt
- [ ] Set up Google Search Console
- [ ] Optimize meta tags and structured data
- [ ] Configure social media previews

## Hosting Platforms

### **Vercel (Recommended)**
```bash
npm install -g vercel
vercel --prod
```

### **Netlify**
```bash
npm run build
# Upload dist folder to Netlify
```

### **AWS S3 + CloudFront**
```bash
aws s3 sync build/ s3://your-bucket-name
aws cloudfront create-invalidation --distribution-id YOUR_ID --paths "/*"
```

## Environment Variables

### **Required**
- `REACT_APP_FIREBASE_API_KEY`
- `REACT_APP_FIREBASE_AUTH_DOMAIN`
- `REACT_APP_FIREBASE_PROJECT_ID`
- `REACT_APP_STRIPE_PUBLISHABLE_KEY`

### **Optional**
- `REACT_APP_METAAPI_TOKEN`
- `REACT_APP_GOOGLE_ANALYTICS_ID`
- `REACT_APP_ENABLE_PWA`

## Performance Targets

### **Core Web Vitals**
- First Contentful Paint (FCP): < 1.8s
- Largest Contentful Paint (LCP): < 2.5s
- First Input Delay (FID): < 100ms
- Cumulative Layout Shift (CLS): < 0.1

### **Lighthouse Scores**
- Performance: > 90
- Accessibility: > 95
- Best Practices: > 90
- SEO: > 95

## Troubleshooting

### **Common Issues**
1. **Build fails**: Check Node.js version (requires v16+)
2. **Firebase errors**: Verify environment variables
3. **Stripe issues**: Check API keys and webhook endpoints
4. **MetaApi connection**: Verify token and account permissions

### **Debug Commands**
```bash
# Check bundle size
npm run analyze

# Run lighthouse audit
npm run lighthouse

# Test with coverage
npm run test:coverage
```
