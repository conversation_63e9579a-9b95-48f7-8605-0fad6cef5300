module.exports = {
  style: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer'),
      ],
    },
  },
  webpack: {
    configure: (webpackConfig) => {
      // Disable ESLint to avoid compatibility issues with older react-scripts
      const eslintRule = webpackConfig.module.rules.find(
        rule => rule.use && rule.use.some(use => use.loader && use.loader.includes('eslint'))
      );
      if (eslintRule) {
        webpackConfig.module.rules = webpackConfig.module.rules.filter(rule => rule !== eslintRule);
      }

      return webpackConfig;
    },
  },
}