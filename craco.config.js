const webpack = require('webpack');

module.exports = {
  style: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer'),
      ],
    },
  },
  webpack: {
    configure: (webpackConfig) => {
      // Disable ESLint to avoid compatibility issues with older react-scripts
      const eslintRule = webpackConfig.module.rules.find(
        rule => rule.use && rule.use.some(use => use.loader && use.loader.includes('eslint'))
      );
      if (eslintRule) {
        webpackConfig.module.rules = webpackConfig.module.rules.filter(rule => rule !== eslintRule);
      }

      // Add Node.js polyfills for older webpack
      webpackConfig.node = {
        ...webpackConfig.node,
        process: 'mock',
        global: true,
        Buffer: 'mock',
        setImmediate: true,
        fs: 'empty',
        net: 'empty',
        tls: 'empty',
        crypto: 'empty',
        stream: 'empty',
        http: 'empty',
        https: 'empty',
        zlib: 'empty',
        path: 'empty',
        url: 'empty',
        util: 'empty',
        assert: 'empty',
        os: 'empty'
      };

      // Add plugins for polyfills
      webpackConfig.plugins.push(
        new webpack.ProvidePlugin({
          process: 'process/browser',
          Buffer: ['buffer', 'Buffer'],
          global: 'global/window',
        })
      );

      // Add resolve alias for better compatibility
      webpackConfig.resolve.alias = {
        ...webpackConfig.resolve.alias,
        process: 'process/browser',
        buffer: 'buffer',
        stream: 'stream-browserify',
        crypto: 'crypto-browserify',
        http: 'stream-http',
        https: 'https-browserify',
        os: 'os-browserify/browser',
        url: 'url',
        assert: 'assert',
        util: 'util'
      };

      return webpackConfig;
    },
  },
}