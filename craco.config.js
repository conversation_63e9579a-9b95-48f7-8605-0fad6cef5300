module.exports = {
  style: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer'),
      ],
    },
  },
  webpack: {
    configure: (webpackConfig) => {
      // Disable ESLint to avoid compatibility issues with older react-scripts
      const eslintRule = webpackConfig.module.rules.find(
        rule => rule.use && rule.use.some(use => use.loader && use.loader.includes('eslint'))
      );
      if (eslintRule) {
        webpackConfig.module.rules = webpackConfig.module.rules.filter(rule => rule !== eslintRule);
      }

      // Remove webpack hot dev client to avoid URL.format issues
      if (webpackConfig.entry && Array.isArray(webpackConfig.entry)) {
        webpackConfig.entry = webpackConfig.entry.filter(entry =>
          !entry.includes('webpackHotDevClient')
        );
      }

      // Add Node.js polyfills for older webpack (react-scripts 3.x uses webpack 4)
      webpackConfig.node = {
        ...webpackConfig.node,
        process: 'mock',
        global: true,
        Buffer: 'mock',
        setImmediate: true,
        fs: 'empty',
        net: 'empty',
        tls: 'empty',
        crypto: 'empty',
        stream: 'empty',
        http: 'empty',
        https: 'empty',
        zlib: 'empty',
        path: 'empty',
        url: 'empty',
        util: 'empty',
        assert: 'empty',
        os: 'empty'
      };

      return webpackConfig;
    },
  },
}